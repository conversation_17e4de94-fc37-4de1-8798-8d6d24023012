// 游戏核心数据类型定义

// 稀有度枚举
export enum Rarity {
  NORMAL = 'normal',
  RARE = 'rare',
  SPECIAL = 'special'
}

// 时间段枚举
export enum TimePeriod {
  MORNING = 'morning',
  AFTERNOON = 'afternoon',
  EVENING = 'evening'
}

// 角色属性
export interface CharacterAttributes {
  charm: number      // 魅力
  skill: number      // 技艺
  stamina: number    // 体力
  wisdom: number     // 智慧
}

// 角色数据结构
export interface Character {
  id: string
  name: string
  description: string
  rarity: Rarity
  level: number
  exp: number
  maxExp: number
  attributes: CharacterAttributes
  isWorking: boolean
  workLocation?: string
  avatar?: string
  unlockCondition?: string
  storylineCompleted: boolean
}

// 物品类别枚举
export enum ItemCategory {
  MATERIALS = 'materials',
  PRODUCTS = 'products',
  SPECIAL = 'special'
}

// 物品数据结构
export interface Item {
  id: string
  name: string
  description: string
  category: ItemCategory
  value: number
  stackable: boolean
  maxStack?: number
}

// 库存物品
export interface InventoryItem {
  itemId: string
  quantity: number
}

// 工坊类型枚举
export enum WorkshopType {
  HALL = 'hall',           // 会客大厅
  HOLY_WATER = 'holy_water', // 圣水工坊
  HONEY = 'honey',         // 蜜酿工坊
  LOTION = 'lotion',       // 乳液工坊
  NECTAR = 'nectar'        // 琼浆工坊
}

// 派遣任务
export interface DispatchTask {
  id: string
  workshopType: WorkshopType
  characterIds: string[]
  startTime: number
  duration: number
  isCompleted: boolean
  rewards: {
    exp: number
    money?: number
    items?: { itemId: string; quantity: number }[]
  }
}

// 发展树技能
export interface Skill {
  id: string
  name: string
  description: string
  cost: number
  unlocked: boolean
  prerequisites: string[]
  effects: string[]
  branch: 'production' | 'business' | 'character'
}

// 任务优先级枚举
export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

// 游戏任务
export interface GameTask {
  id: string
  title: string
  description: string
  priority: TaskPriority
  completed: boolean
  deadline?: number
  createdAt: number
  rewards?: {
    exp?: number
    money?: number
    items?: { itemId: string; quantity: number }[]
  }
}

// 游戏事件类型枚举
export enum EventType {
  STORY = 'story',
  TASK = 'task',
  SPECIAL = 'special',
  SYSTEM = 'system'
}

// 游戏事件
export interface GameEvent {
  id: string
  title: string
  description: string
  type: EventType
  time: string
  date: number
  isCompleted?: boolean
}

// 农场地块
export interface FarmPlot {
  id: number
  isEmpty: boolean
  cropType?: string
  growthLevel: number // 0-100
  isWatered: boolean
  isFertilized: boolean
  plantedTime?: number
  harvestTime?: number
}

// 游戏时间状态
export interface GameTime {
  day: number
  period: TimePeriod
  dayOfWeek: number // 1-7 (周一到周日)
  totalDays: number
}

// 玩家状态
export interface PlayerStatus {
  energy: number
  maxEnergy: number
  money: number
  exp: number
  level: number
  maxExp: number
}

// 每日活动状态
export interface DailyActivities {
  meal: boolean
  bath: boolean
}

// 解锁功能状态
export interface UnlockedFeatures {
  doubleRest: boolean
  groupRest: boolean
  blackMarket: boolean
  advancedWorkshops: boolean
}

// 游戏设置
export interface GameSettings {
  soundVolume: number
  musicVolume: number
  autoSave: boolean
  language: string
}

// 游戏统计
export interface GameStats {
  totalPlayTime: number
  charactersRecruited: number
  itemsProduced: number
  moneyEarned: number
  tasksCompleted: number
}

// 完整的游戏存档数据
export interface GameSave {
  id: string
  version: string
  createdAt: number
  lastSaved: number
  
  // 游戏状态
  gameTime: GameTime
  playerStatus: PlayerStatus
  
  // 游戏内容
  characters: Character[]
  inventory: InventoryItem[]
  skills: Skill[]
  tasks: GameTask[]
  events: GameEvent[]
  farmPlots: FarmPlot[]
  dispatchTasks: DispatchTask[]
  
  // 游戏进度
  dailyActivities: DailyActivities
  unlockedFeatures: UnlockedFeatures
  completedStorylines: string[]
  
  // 设置和统计
  settings: GameSettings
  stats: GameStats
}

// API 响应类型
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
}

// 分页数据
export interface PaginatedData<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
}
