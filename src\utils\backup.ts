import { saveManager } from '@/services/saveManager'
import { GameSave } from '@/types/game'

// 备份和恢复工具类
export class BackupUtils {
  // 下载存档文件
  static downloadSave(save: GameSave, filename?: string): void {
    const saveData = JSON.stringify(save, null, 2)
    const blob = new Blob([saveData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = filename || `nectar_game_save_${save.gameTime.day}.json`
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
  }

  // 上传存档文件
  static uploadSave(): Promise<GameSave> {
    return new Promise((resolve, reject) => {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.json'
      
      input.onchange = async (event) => {
        const file = (event.target as HTMLInputElement).files?.[0]
        if (!file) {
          reject(new Error('No file selected'))
          return
        }
        
        try {
          const text = await file.text()
          const save = await saveManager.importSave(text)
          resolve(save)
        } catch (error) {
          reject(error)
        }
      }
      
      input.click()
    })
  }

  // 导出所有存档
  static async exportAllSaves(): Promise<void> {
    const saves = await saveManager.getAllSaveInfo()
    const allSaves: GameSave[] = []
    
    for (const saveInfo of saves) {
      const save = await saveManager.loadGame(saveInfo.id)
      if (save) {
        allSaves.push(save)
      }
    }
    
    const exportData = {
      version: '1.0.0',
      exportedAt: Date.now(),
      saves: allSaves
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `nectar_game_all_saves_${new Date().toISOString().split('T')[0]}.json`
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
  }

  // 导入所有存档
  static importAllSaves(): Promise<number> {
    return new Promise((resolve, reject) => {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.json'
      
      input.onchange = async (event) => {
        const file = (event.target as HTMLInputElement).files?.[0]
        if (!file) {
          reject(new Error('No file selected'))
          return
        }
        
        try {
          const text = await file.text()
          const data = JSON.parse(text)
          
          if (!data.saves || !Array.isArray(data.saves)) {
            reject(new Error('Invalid export file format'))
            return
          }
          
          let importedCount = 0
          
          for (const save of data.saves) {
            try {
              await saveManager.importSave(JSON.stringify(save))
              importedCount++
            } catch (error) {
              console.warn('Failed to import save:', error)
            }
          }
          
          resolve(importedCount)
        } catch (error) {
          reject(error)
        }
      }
      
      input.click()
    })
  }

  // 创建快速备份到剪贴板
  static async copyToClipboard(save: GameSave): Promise<void> {
    const saveData = JSON.stringify(save)
    
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(saveData)
    } else {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = saveData
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        document.execCommand('copy')
      } catch (error) {
        throw new Error('Failed to copy to clipboard')
      } finally {
        document.body.removeChild(textArea)
      }
    }
  }

  // 从剪贴板恢复
  static async pasteFromClipboard(): Promise<GameSave> {
    let text: string
    
    if (navigator.clipboard && navigator.clipboard.readText) {
      text = await navigator.clipboard.readText()
    } else {
      throw new Error('Clipboard API not supported')
    }
    
    return await saveManager.importSave(text)
  }

  // 验证存档文件
  static validateSaveFile(file: File): Promise<boolean> {
    return new Promise((resolve) => {
      const reader = new FileReader()
      
      reader.onload = () => {
        try {
          const data = JSON.parse(reader.result as string)
          // 简单验证存档结构
          const isValid = data && 
                         typeof data.id === 'string' &&
                         typeof data.version === 'string' &&
                         typeof data.gameTime === 'object' &&
                         typeof data.playerStatus === 'object'
          resolve(isValid)
        } catch {
          resolve(false)
        }
      }
      
      reader.onerror = () => resolve(false)
      reader.readAsText(file)
    })
  }

  // 压缩存档数据
  static compressSave(save: GameSave): string {
    // 移除不必要的字段以减小大小
    const compressed = {
      ...save,
      // 可以在这里添加压缩逻辑
    }
    
    return JSON.stringify(compressed)
  }

  // 解压存档数据
  static decompressSave(compressedData: string): GameSave {
    const save = JSON.parse(compressedData)
    
    // 可以在这里添加解压逻辑
    
    return save
  }

  // 获取存档摘要信息
  static getSaveSummary(save: GameSave): {
    day: number
    level: number
    money: number
    charactersCount: number
    playtime: string
  } {
    const playtimeHours = Math.floor(save.stats.totalPlayTime / (1000 * 60 * 60))
    const playtimeMinutes = Math.floor((save.stats.totalPlayTime % (1000 * 60 * 60)) / (1000 * 60))
    
    return {
      day: save.gameTime.day,
      level: save.playerStatus.level,
      money: save.playerStatus.money,
      charactersCount: save.characters.length,
      playtime: `${playtimeHours}h ${playtimeMinutes}m`
    }
  }
}

// 自动备份管理器
export class AutoBackupManager {
  private static instance: AutoBackupManager
  private backupInterval: number | null = null
  private readonly maxBackups = 5
  private readonly backupIntervalMs = 30 * 60 * 1000 // 30分钟

  static getInstance(): AutoBackupManager {
    if (!AutoBackupManager.instance) {
      AutoBackupManager.instance = new AutoBackupManager()
    }
    return AutoBackupManager.instance
  }

  // 启动自动备份
  start(): void {
    this.stop()
    
    this.backupInterval = window.setInterval(async () => {
      try {
        await this.createBackup()
      } catch (error) {
        console.error('Auto backup failed:', error)
      }
    }, this.backupIntervalMs)
  }

  // 停止自动备份
  stop(): void {
    if (this.backupInterval) {
      clearInterval(this.backupInterval)
      this.backupInterval = null
    }
  }

  // 创建备份
  private async createBackup(): Promise<void> {
    const currentSave = saveManager.getCurrentSave()
    if (!currentSave) return

    const backupKey = `backup_${Date.now()}`
    const backupData = BackupUtils.compressSave(currentSave)
    
    // 保存到localStorage
    try {
      localStorage.setItem(backupKey, backupData)
      
      // 清理旧备份
      await this.cleanupOldBackups()
    } catch (error) {
      console.warn('Failed to create backup:', error)
    }
  }

  // 清理旧备份
  private async cleanupOldBackups(): Promise<void> {
    const backupKeys = Object.keys(localStorage)
      .filter(key => key.startsWith('backup_'))
      .sort()
    
    while (backupKeys.length > this.maxBackups) {
      const oldestKey = backupKeys.shift()
      if (oldestKey) {
        localStorage.removeItem(oldestKey)
      }
    }
  }

  // 获取所有备份
  getBackups(): Array<{ key: string; timestamp: number; summary: any }> {
    const backups = []
    
    for (const key of Object.keys(localStorage)) {
      if (key.startsWith('backup_')) {
        try {
          const data = localStorage.getItem(key)
          if (data) {
            const save = BackupUtils.decompressSave(data)
            const timestamp = parseInt(key.replace('backup_', ''))
            const summary = BackupUtils.getSaveSummary(save)
            
            backups.push({ key, timestamp, summary })
          }
        } catch (error) {
          console.warn('Failed to parse backup:', key, error)
        }
      }
    }
    
    return backups.sort((a, b) => b.timestamp - a.timestamp)
  }

  // 恢复备份
  async restoreBackup(backupKey: string): Promise<GameSave> {
    const data = localStorage.getItem(backupKey)
    if (!data) {
      throw new Error('Backup not found')
    }
    
    const save = BackupUtils.decompressSave(data)
    return await saveManager.importSave(JSON.stringify(save))
  }

  // 删除备份
  deleteBackup(backupKey: string): void {
    localStorage.removeItem(backupKey)
  }
}
