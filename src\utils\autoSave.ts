import { gameStore } from '@/stores/gameStore'

// 自动保存管理器
export class AutoSaveManager {
  private static instance: AutoSaveManager
  private saveInterval: number | null = null
  private readonly saveIntervalMs = 5 * 60 * 1000 // 5分钟自动保存
  private lastSaveTime = 0
  private isEnabled = true

  static getInstance(): AutoSaveManager {
    if (!AutoSaveManager.instance) {
      AutoSaveManager.instance = new AutoSaveManager()
    }
    return AutoSaveManager.instance
  }

  // 启动自动保存
  start(): void {
    if (!this.isEnabled) return

    this.stop() // 先停止之前的定时器

    this.saveInterval = window.setInterval(async () => {
      try {
        await this.performAutoSave()
      } catch (error) {
        console.error('Auto save failed:', error)
      }
    }, this.saveIntervalMs)

    console.log('Auto save started')
  }

  // 停止自动保存
  stop(): void {
    if (this.saveInterval) {
      clearInterval(this.saveInterval)
      this.saveInterval = null
      console.log('Auto save stopped')
    }
  }

  // 执行自动保存
  private async performAutoSave(): Promise<void> {
    const now = Date.now()

    // 避免频繁保存
    if (now - this.lastSaveTime < this.saveIntervalMs) {
      return
    }

    // 检查是否有游戏数据
    if (!gameStore.currentSave.value) {
      return
    }

    try {
      await gameStore.saveGame()
      this.lastSaveTime = now
      console.log('Auto save completed at', new Date().toLocaleTimeString())
    } catch (error) {
      console.error('Auto save error:', error)
      throw error
    }
  }

  // 手动触发保存
  async forceSave(): Promise<void> {
    if (!gameStore.currentSave.value) {
      console.log('No game data to save, skipping save operation')
      return
    }

    try {
      await gameStore.saveGame()
      this.lastSaveTime = Date.now()
      console.log('Manual save completed')
    } catch (error) {
      console.error('Manual save error:', error)
      throw error
    }
  }

  // 启用/禁用自动保存
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled

    if (enabled) {
      this.start()
    } else {
      this.stop()
    }
  }

  // 检查是否启用
  isAutoSaveEnabled(): boolean {
    return this.isEnabled
  }

  // 获取上次保存时间
  getLastSaveTime(): number {
    return this.lastSaveTime
  }

  // 获取距离上次保存的时间
  getTimeSinceLastSave(): number {
    return Date.now() - this.lastSaveTime
  }

  // 格式化时间显示
  formatTimeSinceLastSave(): string {
    const timeDiff = this.getTimeSinceLastSave()
    const minutes = Math.floor(timeDiff / (1000 * 60))
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000)

    if (minutes > 0) {
      return `${minutes}分${seconds}秒前`
    } else {
      return `${seconds}秒前`
    }
  }

  // 销毁管理器
  destroy(): void {
    this.stop()
    AutoSaveManager.instance = null as any
  }
}

// 创建全局实例
export const autoSaveManager = AutoSaveManager.getInstance()

// 游戏状态变化监听器
export class GameStateWatcher {
  private static instance: GameStateWatcher
  private watchers: (() => void)[] = []
  private isWatching = false

  static getInstance(): GameStateWatcher {
    if (!GameStateWatcher.instance) {
      GameStateWatcher.instance = new GameStateWatcher()
    }
    return GameStateWatcher.instance
  }

  // 开始监听游戏状态变化
  startWatching(): void {
    if (this.isWatching) return

    this.isWatching = true

    // 监听重要的游戏状态变化
    this.watchPlayerStatus()
    this.watchInventory()
    this.watchCharacters()

    console.log('Game state watching started')
  }

  // 停止监听
  stopWatching(): void {
    this.watchers.forEach(unwatch => unwatch())
    this.watchers = []
    this.isWatching = false

    console.log('Game state watching stopped')
  }

  // 监听玩家状态变化
  private watchPlayerStatus(): void {
    // 这里可以使用Vue的watch API来监听状态变化
    // 当检测到重要变化时触发保存
  }

  // 监听库存变化
  private watchInventory(): void {
    // 监听库存变化
  }

  // 监听角色变化
  private watchCharacters(): void {
    // 监听角色变化
  }

  // 触发状态变化保存
  private onStateChange(): void {
    // 延迟保存，避免频繁保存
    setTimeout(async () => {
      try {
        await autoSaveManager.forceSave()
      } catch (error) {
        console.error('State change save failed:', error)
      }
    }, 1000)
  }
}

// 创建全局状态监听器实例
export const gameStateWatcher = GameStateWatcher.getInstance()

// 初始化自动保存系统
export function initializeAutoSave(): void {
  // 启动自动保存
  autoSaveManager.start()

  // 启动状态监听
  gameStateWatcher.startWatching()

  // 页面卸载时保存
  window.addEventListener('beforeunload', async (event) => {
    try {
      await autoSaveManager.forceSave()
    } catch (error) {
      console.error('Before unload save failed:', error)
    }
  })

  // 页面隐藏时保存
  document.addEventListener('visibilitychange', async () => {
    if (document.hidden) {
      try {
        await autoSaveManager.forceSave()
      } catch (error) {
        console.error('Visibility change save failed:', error)
      }
    }
  })
}

// 清理自动保存系统
export function cleanupAutoSave(): void {
  autoSaveManager.destroy()
  gameStateWatcher.stopWatching()
}
