import { createRouter, createWebHistory } from 'vue-router'
import GameLayout from '@/layouts/GameLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/game/start'
    },
    {
      path: '/game',
      component: GameLayout,
      children: [
        {
          path: 'start',
          name: 'start',
          component: () => import('@/views/StartView.vue')
        },
        {
          path: 'map',
          name: 'map',
          component: () => import('@/views/MapView.vue')
        },
        {
          path: 'characters',
          name: 'characters',
          component: () => import('@/views/CharactersView.vue')
        },
        {
          path: 'inventory',
          name: 'inventory',
          component: () => import('@/views/InventoryView.vue')
        },
        {
          path: 'calendar',
          name: 'calendar',
          component: () => import('@/views/CalendarView.vue')
        },
        {
          path: 'tree',
          name: 'tree',
          component: () => import('@/views/TreeView.vue')
        },
        // 琉璃胭脂坊相关路由
        {
          path: 'workshop',
          name: 'workshop',
          component: () => import('@/views/workshop/WorkshopView.vue')
        },
        {
          path: 'hall',
          name: 'hall',
          component: () => import('@/views/workshop/HallView.vue')
        },
        {
          path: 'holy-water',
          name: 'holy-water',
          component: () => import('@/views/workshop/HolyWaterView.vue')
        },
        {
          path: 'honey',
          name: 'honey',
          component: () => import('@/views/workshop/HoneyView.vue')
        },
        // 下午场景路由
        {
          path: 'market',
          name: 'market',
          component: () => import('@/views/afternoon/MarketView.vue')
        },
        {
          path: 'garden',
          name: 'garden',
          component: () => import('@/views/afternoon/GardenView.vue')
        },
        {
          path: 'farm',
          name: 'farm',
          component: () => import('@/views/afternoon/FarmView.vue')
        },
        // 晚间寝室路由
        {
          path: 'bedroom',
          name: 'bedroom',
          component: () => import('@/views/evening/BedroomView.vue')
        }
      ]
    }
  ],
})

export default router
