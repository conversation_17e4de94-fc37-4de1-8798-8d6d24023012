<template>
  <div class="holy-water-view">
    <a-card title="圣水工坊" class="workshop-card">
      <template #extra>
        <a-space>
          <a-tag color="blue">可用槽位: {{ workshopStatus.availableSlots }}/{{ workshopStatus.totalSlots }}</a-tag>
          <a-button @click="goBack">返回工坊</a-button>
        </a-space>
      </template>

      <div class="workshop-content">
        <!-- 材料需求 -->
        <a-card title="材料需求" class="materials-section">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="所需材料">清泉水 x1</a-descriptions-item>
            <a-descriptions-item label="当前库存">{{ getItemQuantity('spring_water') }} 个</a-descriptions-item>
            <a-descriptions-item label="生产时间">1 小时</a-descriptions-item>
            <a-descriptions-item label="产出物品">圣水 x2</a-descriptions-item>
          </a-descriptions>
          
          <div style="margin-top: 16px;">
            <a-alert 
              v-if="getItemQuantity('spring_water') === 0"
              message="材料不足"
              description="请前往翡翠商会购买清泉水"
              type="warning"
              show-icon
            />
          </div>
        </a-card>

        <!-- 派遣区域 -->
        <a-card title="派遣角色" class="dispatch-section" style="margin-top: 16px;">
          <div class="character-selection">
            <h4>选择角色进行圣水制作：</h4>
            <a-row gutter={16}>
              <a-col 
                v-for="character in availableCharacters" 
                :key="character.id" 
                span={6}
              >
                <a-card 
                  hoverable 
                  class="character-card"
                  :class="{ selected: selectedCharacters.includes(character.id) }"
                  @click="toggleCharacterSelection(character.id)"
                >
                  <template #cover>
                    <div class="character-avatar">
                      <UserOutlined />
                    </div>
                  </template>
                  <a-card-meta :title="character.name" :description="`等级 ${character.level}`" />
                  <div class="character-stats">
                    <a-tag color="blue">技艺: {{ character.attributes.skill }}</a-tag>
                    <a-tag color="green">智慧: {{ character.attributes.wisdom }}</a-tag>
                  </div>
                </a-card>
              </a-col>
            </a-row>
            
            <div class="dispatch-actions" style="margin-top: 16px;">
              <a-space>
                <a-button 
                  type="primary" 
                  size="large"
                  @click="startDispatch"
                  :disabled="!canDispatch"
                  :loading="dispatching"
                >
                  开始制作 ({{ selectedCharacters.length }} 人)
                </a-button>
                <a-button @click="clearSelection">清空选择</a-button>
              </a-space>
            </div>
          </div>
        </a-card>

        <!-- 当前任务 -->
        <a-card title="进行中的制作" class="active-tasks-section" style="margin-top: 16px;">
          <div v-if="activeTasks.length === 0" class="empty-tasks">
            <a-empty description="暂无进行中的制作任务" />
          </div>
          <div v-else>
            <a-list
              :data-source="activeTasks"
              item-layout="horizontal"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <template #actions>
                    <a-button 
                      v-if="getTaskProgress(item) >= 100"
                      type="primary"
                      @click="completeTask(item.id)"
                    >
                      完成
                    </a-button>
                    <a-button 
                      v-else
                      danger
                      @click="cancelTask(item.id)"
                    >
                      取消
                    </a-button>
                  </template>
                  <a-list-item-meta>
                    <template #title>
                      <span>圣水制作 - {{ item.characterIds.length }} 人</span>
                    </template>
                    <template #description>
                      <div>
                        <div>参与角色: {{ getCharacterNames(item.characterIds).join(', ') }}</div>
                        <div style="margin-top: 8px;">
                          <a-progress 
                            :percent="getTaskProgress(item)" 
                            :status="getTaskProgress(item) >= 100 ? 'success' : 'active'"
                          />
                        </div>
                        <div v-if="getTaskProgress(item) < 100" style="margin-top: 4px;">
                          剩余时间: {{ formatRemainingTime(getTaskRemainingTime(item)) }}
                        </div>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </div>
        </a-card>

        <!-- 制作说明 -->
        <a-card title="制作说明" class="info-section" style="margin-top: 16px;">
          <div>
            <h4>圣水制作要点：</h4>
            <ul>
              <li>需要消耗清泉水作为基础材料</li>
              <li>角色的技艺和智慧属性影响制作效率</li>
              <li>多人协作可以提高成功率</li>
              <li>制作完成后获得圣水，可用于农场浇灌</li>
            </ul>
          </div>
        </a-card>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { UserOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { gameStore } from '@/stores/gameStore'
import { dispatchStore } from '@/stores/dispatchStore'
import { WorkshopType } from '@/types/game'

const router = useRouter()

// 选中的角色
const selectedCharacters = ref<string[]>([])
const dispatching = ref(false)

// 从状态管理获取数据
const { availableCharacters, getItemQuantity } = gameStore
const { 
  dispatchCharacters, 
  completeTask: completeDispatchTask, 
  cancelTask: cancelDispatchTask,
  getTaskProgress,
  getTaskRemainingTime,
  formatRemainingTime,
  getWorkshopStatus
} = dispatchStore

// 工坊状态
const workshopStatus = computed(() => getWorkshopStatus(WorkshopType.HOLY_WATER))

// 当前活跃任务
const activeTasks = computed(() => workshopStatus.value.activeTasks)

// 是否可以派遣
const canDispatch = computed(() => {
  return selectedCharacters.value.length > 0 && 
         selectedCharacters.value.length <= workshopStatus.value.availableSlots &&
         getItemQuantity('spring_water') > 0
})

// 切换角色选择
const toggleCharacterSelection = (characterId: string) => {
  const index = selectedCharacters.value.indexOf(characterId)
  if (index > -1) {
    selectedCharacters.value.splice(index, 1)
  } else {
    if (selectedCharacters.value.length < workshopStatus.value.availableSlots) {
      selectedCharacters.value.push(characterId)
    } else {
      message.warning('已达到最大派遣数量')
    }
  }
}

// 清空选择
const clearSelection = () => {
  selectedCharacters.value = []
}

// 开始派遣
const startDispatch = async () => {
  if (!canDispatch.value) {
    message.error('无法开始制作')
    return
  }

  if (getItemQuantity('spring_water') === 0) {
    message.error('清泉水不足')
    return
  }

  dispatching.value = true
  
  try {
    // 消耗角色体力
    for (const charId of selectedCharacters.value) {
      if (!gameStore.consumeEnergy(1)) {
        message.error('体力不足')
        return
      }
    }

    if (dispatchCharacters(WorkshopType.HOLY_WATER, selectedCharacters.value)) {
      message.success('圣水制作开始！')
      selectedCharacters.value = []
    } else {
      message.error('派遣失败')
    }
  } catch (error) {
    message.error('派遣失败')
    console.error(error)
  } finally {
    dispatching.value = false
  }
}

// 完成任务
const completeTask = (taskId: string) => {
  if (completeDispatchTask(taskId)) {
    message.success('圣水制作完成，获得奖励！')
  } else {
    message.error('完成任务失败')
  }
}

// 取消任务
const cancelTask = (taskId: string) => {
  if (cancelDispatchTask(taskId)) {
    message.success('已取消制作')
  } else {
    message.error('取消任务失败')
  }
}

// 获取角色名称
const getCharacterNames = (characterIds: string[]): string[] => {
  return characterIds.map(id => {
    const character = gameStore.characters.value.find(c => c.id === id)
    return character ? character.name : '未知角色'
  })
}

const goBack = () => {
  router.push({ name: 'workshop' })
}
</script>

<style scoped>
.holy-water-view {
  height: 100%;
  padding: 16px;
}

.workshop-card {
  height: 100%;
}

.workshop-content {
  padding: 16px 0;
}

.character-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.character-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.character-card.selected {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.character-avatar {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.character-stats {
  margin-top: 8px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.dispatch-actions {
  text-align: center;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
}

.empty-tasks {
  text-align: center;
  padding: 40px;
}

.info-section ul {
  margin: 0;
  padding-left: 20px;
}

.info-section li {
  margin-bottom: 4px;
}
</style>
