# 角色数据目录

此目录用于存放游戏角色的JSON数据文件。

## 文件结构

- `templates/` - 角色模板数据
- `generated/` - 自动生成的角色数据
- `special/` - 特殊角色数据

## 角色数据格式

```json
{
  "id": "char_001",
  "name": "角色名称",
  "description": "角色描述",
  "rarity": "normal|rare|special",
  "portraitId": "portrait_001",
  "attributes": {
    "charm": 15,
    "skill": 12,
    "stamina": 18,
    "wisdom": 10
  },
  "level": 1,
  "exp": 0,
  "maxExp": 100,
  "isWorking": false,
  "storylineCompleted": false,
  "unlockConditions": [],
  "specialAbilities": []
}
```
