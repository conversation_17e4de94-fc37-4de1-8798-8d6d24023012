import { Item, Character, Skill, ItemCategory, Rarity, WorkshopType } from '@/types/game'

// 游戏版本
export const GAME_VERSION = '1.0.0'

// 数据库配置
export const DB_NAME = 'NectarGameDB'
export const DB_VERSION = 1

// 时间配置
export const TIME_CONFIG = {
  REAL_TIME_TO_GAME_TIME: 1000, // 1秒 = 1游戏分钟
  DISPATCH_DURATION: 60 * 60 * 1000, // 派遣任务持续1小时
  CROP_GROWTH_TIME: 30 * 60 * 1000, // 作物生长30分钟
}

// 玩家初始状态
export const INITIAL_PLAYER_STATUS = {
  energy: 10,
  maxEnergy: 10,
  money: 1000,
  exp: 0,
  level: 1,
  maxExp: 100
}

// 基础物品数据
export const BASE_ITEMS: Item[] = [
  // 材料
  {
    id: 'seed',
    name: '灵果种子',
    description: '用于种植的神奇种子',
    category: ItemCategory.MATERIALS,
    value: 5,
    stackable: true,
    maxStack: 999
  },
  {
    id: 'spring_water',
    name: '清泉水',
    description: '制作圣水的基础材料',
    category: ItemCategory.MATERIALS,
    value: 10,
    stackable: true,
    maxStack: 999
  },
  {
    id: 'flower_nectar',
    name: '花蜜',
    description: '制作蜜酿的核心材料',
    category: ItemCategory.MATERIALS,
    value: 15,
    stackable: true,
    maxStack: 999
  },
  {
    id: 'spirit_fruit',
    name: '灵果',
    description: '农场收获的神奇果实',
    category: ItemCategory.MATERIALS,
    value: 20,
    stackable: true,
    maxStack: 999
  },

  // 商品
  {
    id: 'holy_water',
    name: '圣水',
    description: '纯净的圣水，可用于浇灌',
    category: ItemCategory.PRODUCTS,
    value: 30,
    stackable: true,
    maxStack: 999
  },
  {
    id: 'honey_wine',
    name: '蜜酿',
    description: '香甜的蜜酿，深受欢迎',
    category: ItemCategory.PRODUCTS,
    value: 80,
    stackable: true,
    maxStack: 999
  },
  {
    id: 'lotion',
    name: '乳液',
    description: '滋润的乳液，可用作肥料',
    category: ItemCategory.PRODUCTS,
    value: 150,
    stackable: true,
    maxStack: 999
  },
  {
    id: 'nectar',
    name: '琼浆',
    description: '珍贵的琼浆，招募稀有角色必需',
    category: ItemCategory.PRODUCTS,
    value: 300,
    stackable: true,
    maxStack: 999
  },

  // 特殊物品
  {
    id: 'mysterious_gem',
    name: '神秘宝石',
    description: '散发着神秘光芒的宝石',
    category: ItemCategory.SPECIAL,
    value: 1000,
    stackable: true,
    maxStack: 99
  }
]

// 初始角色数据
export const INITIAL_CHARACTERS: Character[] = [
  {
    id: 'xiaoya',
    name: '小雅',
    description: '温柔善良的少女，是你的第一个伙伴',
    rarity: Rarity.NORMAL,
    level: 1,
    exp: 0,
    maxExp: 100,
    attributes: {
      charm: 15,
      skill: 12,
      stamina: 18,
      wisdom: 10
    },
    isWorking: false,
    storylineCompleted: false
  }
]

// 基础技能数据
export const BASE_SKILLS: Skill[] = [
  // 生产分支
  {
    id: 'holy_water_production',
    name: '圣水制作',
    description: '解锁圣水工坊，可以生产圣水',
    cost: 0, // 初始解锁
    unlocked: true,
    prerequisites: [],
    effects: ['解锁圣水工坊', '可派遣2个角色生产圣水'],
    branch: 'production'
  },
  {
    id: 'honey_production',
    name: '蜜酿制作',
    description: '解锁蜜酿工坊，可以生产蜜酿',
    cost: 0, // 初始解锁
    unlocked: true,
    prerequisites: [],
    effects: ['解锁蜜酿工坊', '可派遣2个角色生产蜜酿'],
    branch: 'production'
  },
  {
    id: 'lotion_production',
    name: '乳液制作',
    description: '解锁乳液工坊，可以生产乳液',
    cost: 2,
    unlocked: false,
    prerequisites: ['holy_water_production'],
    effects: ['解锁乳液工坊', '可派遣2个角色生产乳液'],
    branch: 'production'
  },
  {
    id: 'nectar_production',
    name: '琼浆制作',
    description: '解锁琼浆工坊，可以生产琼浆',
    cost: 3,
    unlocked: false,
    prerequisites: ['honey_production', 'lotion_production'],
    effects: ['解锁琼浆工坊', '可派遣2个角色生产琼浆'],
    branch: 'production'
  },

  // 经营分支
  {
    id: 'expand_workshop',
    name: '扩展工坊',
    description: '增加工坊的派遣槽位',
    cost: 2,
    unlocked: false,
    prerequisites: ['holy_water_production', 'honey_production'],
    effects: ['所有工坊派遣槽位+2'],
    branch: 'business'
  },
  {
    id: 'expand_hall',
    name: '扩展会客大厅',
    description: '增加会客大厅的派遣槽位',
    cost: 2,
    unlocked: false,
    prerequisites: [],
    effects: ['会客大厅派遣槽位+2'],
    branch: 'business'
  },

  // 角色分支
  {
    id: 'character_training',
    name: '角色培养',
    description: '提升角色属性成长速度',
    cost: 2,
    unlocked: false,
    prerequisites: [],
    effects: ['角色经验获得+50%', '属性成长+25%'],
    branch: 'character'
  },
  {
    id: 'advanced_training',
    name: '高级培养',
    description: '大幅提升角色属性成长',
    cost: 3,
    unlocked: false,
    prerequisites: ['character_training'],
    effects: ['角色经验获得+100%', '属性成长+50%'],
    branch: 'character'
  }
]

// 工坊配置
export const WORKSHOP_CONFIG = {
  [WorkshopType.HALL]: {
    name: '会客大厅',
    description: '派遣少女进行歌舞曲艺表演',
    baseSlots: 2,
    maxSlots: 4,
    duration: 60 * 60 * 1000, // 1小时
    rewards: {
      money: 100,
      exp: 20
    },
    requirements: []
  },
  [WorkshopType.HOLY_WATER]: {
    name: '圣水工坊',
    description: '生产圣水',
    baseSlots: 2,
    maxSlots: 6,
    duration: 60 * 60 * 1000,
    rewards: {
      items: [{ itemId: 'holy_water', quantity: 2 }],
      exp: 15
    },
    requirements: [{ itemId: 'spring_water', quantity: 1 }]
  },
  [WorkshopType.HONEY]: {
    name: '蜜酿工坊',
    description: '生产蜜酿',
    baseSlots: 2,
    maxSlots: 6,
    duration: 90 * 60 * 1000, // 1.5小时
    rewards: {
      items: [{ itemId: 'honey_wine', quantity: 1 }],
      exp: 25
    },
    requirements: [{ itemId: 'flower_nectar', quantity: 2 }]
  },
  [WorkshopType.LOTION]: {
    name: '乳液工坊',
    description: '生产乳液',
    baseSlots: 2,
    maxSlots: 6,
    duration: 120 * 60 * 1000, // 2小时
    rewards: {
      items: [{ itemId: 'lotion', quantity: 1 }],
      exp: 35
    },
    requirements: [
      { itemId: 'holy_water', quantity: 2 },
      { itemId: 'spirit_fruit', quantity: 1 }
    ]
  },
  [WorkshopType.NECTAR]: {
    name: '琼浆工坊',
    description: '生产琼浆',
    baseSlots: 2,
    maxSlots: 6,
    duration: 180 * 60 * 1000, // 3小时
    rewards: {
      items: [{ itemId: 'nectar', quantity: 1 }],
      exp: 50
    },
    requirements: [
      { itemId: 'honey_wine', quantity: 2 },
      { itemId: 'lotion', quantity: 1 },
      { itemId: 'spirit_fruit', quantity: 2 }
    ]
  }
}

// 招募配置
export const RECRUIT_CONFIG = {
  normal: {
    cost: { money: 100 },
    successRate: 1.0,
    possibleRarities: [Rarity.NORMAL]
  },
  rare: {
    cost: { money: 500, items: [{ itemId: 'nectar', quantity: 1 }] },
    successRate: 0.8,
    possibleRarities: [Rarity.RARE, Rarity.SPECIAL],
    availabilityChance: 0.3 // 30%概率出现
  }
}

// 市场配置
export const MARKET_CONFIG = {
  materials: [
    { itemId: 'seed', price: 5, stock: 50 },
    { itemId: 'spring_water', price: 10, stock: 30 },
    { itemId: 'flower_nectar', price: 15, stock: 20 }
  ],
  sellableProducts: ['holy_water', 'honey_wine', 'lotion', 'nectar']
}

// 农场配置
export const FARM_CONFIG = {
  plotCount: 9,
  growthStages: {
    0: '种子期',
    25: '发芽期',
    50: '生长期',
    75: '结果期',
    100: '成熟'
  },
  waterBonus: 20, // 浇水增加20%生长
  fertilizeBonus: 30, // 施肥增加30%生长
  baseGrowthTime: 30 * 60 * 1000 // 30分钟基础生长时间
}

// 经验等级配置
export const LEVEL_CONFIG = {
  getExpForLevel: (level: number) => level * 100,
  getMaxLevel: () => 50
}

// 本地存储键名
export const STORAGE_KEYS = {
  GAME_SAVE: 'nectar_game_save',
  SETTINGS: 'nectar_game_settings',
  STATISTICS: 'nectar_game_stats'
}
