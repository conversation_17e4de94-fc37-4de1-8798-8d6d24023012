import { ref, computed } from 'vue'
import type { Skill } from '@/types/game'
import { BASE_SKILLS } from '@/constants/game'

// 技能管理
export function useSkillStore() {
  // 技能列表
  const skills = ref<Skill[]>([...BASE_SKILLS])

  // 可用技能点数
  const availablePoints = ref(5)

  // 计算属性
  const productionSkills = computed(() =>
    skills.value.filter(s => s.branch === 'production')
  )

  const businessSkills = computed(() =>
    skills.value.filter(s => s.branch === 'business')
  )

  const characterSkills = computed(() =>
    skills.value.filter(s => s.branch === 'character')
  )

  const unlockedSkills = computed(() =>
    skills.value.filter(s => s.unlocked)
  )

  const canReset = computed(() =>
    skills.value.some(s => s.unlocked && s.cost > 0)
  )

  // 检查是否可以解锁技能
  const canUnlock = (skill: Skill): boolean => {
    if (skill.unlocked) return false
    if (availablePoints.value < skill.cost) return false

    return skill.prerequisites.every(prereqId =>
      skills.value.find(s => s.id === prereqId)?.unlocked
    )
  }

  // 解锁技能
  const unlockSkill = (skillId: string): boolean => {
    const skill = skills.value.find(s => s.id === skillId)
    if (!skill || !canUnlock(skill)) return false

    skill.unlocked = true
    availablePoints.value -= skill.cost

    return true
  }

  // 重置技能树
  const resetSkills = (): number => {
    let refundPoints = 0

    skills.value.forEach(skill => {
      if (skill.unlocked && skill.cost > 0) {
        skill.unlocked = false
        refundPoints += skill.cost
      }
    })

    availablePoints.value += refundPoints
    return refundPoints
  }

  // 获取技能名称
  const getSkillName = (skillId: string): string => {
    const skill = skills.value.find(s => s.id === skillId)
    return skill ? skill.name : '未知技能'
  }

  // 检查技能是否已解锁
  const isSkillUnlocked = (skillId: string): boolean => {
    const skill = skills.value.find(s => s.id === skillId)
    return skill ? skill.unlocked : false
  }

  // 获得技能点数
  const gainSkillPoints = (points: number) => {
    availablePoints.value += points
  }

  // 加载技能数据
  const loadSkills = (skillsData: Skill[], points: number) => {
    skills.value = skillsData
    availablePoints.value = points
  }

  // 重置所有数据
  const reset = () => {
    skills.value = [...BASE_SKILLS]
    availablePoints.value = 5
  }

  return {
    // 状态
    skills,
    availablePoints,

    // 计算属性
    productionSkills,
    businessSkills,
    characterSkills,
    unlockedSkills,
    canReset,

    // 方法
    canUnlock,
    unlockSkill,
    resetSkills,
    getSkillName,
    isSkillUnlocked,
    gainSkillPoints,
    loadSkills,
    reset
  }
}

// 创建全局技能状态实例
export const skillStore = useSkillStore()
