<template>
  <a-card :title="workshopStatus.name" class="workshop-panel">
    <template #extra>
      <a-tag v-if="!workshopStatus.unlocked" color="red">未解锁</a-tag>
      <a-tag v-else-if="workshopStatus.availableSlots === 0" color="orange">已满</a-tag>
      <a-tag v-else color="green">可用</a-tag>
    </template>

    <!-- 工坊状态 -->
    <div class="workshop-status">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-statistic title="总槽位" :value="workshopStatus.totalSlots" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="使用中" :value="workshopStatus.usedSlots" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="可用" :value="workshopStatus.availableSlots" />
        </a-col>
      </a-row>
    </div>

    <!-- 活跃任务列表 -->
    <div v-if="workshopStatus.activeTasks.length > 0" class="active-tasks">
      <a-divider>进行中的任务</a-divider>
      <div class="task-list">
        <div 
          v-for="task in workshopStatus.activeTasks" 
          :key="task.id"
          class="task-item"
        >
          <a-card size="small">
            <div class="task-header">
              <span class="task-characters">
                {{ getTaskCharacterNames(task) }}
              </span>
              <a-tag :color="getTaskStatusColor(task)">
                {{ getTaskStatusText(task) }}
              </a-tag>
            </div>
            
            <div class="task-progress">
              <a-progress 
                :percent="getTaskProgress(task)" 
                :status="task.isCompleted ? 'success' : 'active'"
                size="small"
              />
              <div class="task-time">
                剩余时间: {{ formatRemainingTime(getTaskRemainingTime(task)) }}
              </div>
            </div>

            <div class="task-actions">
              <a-button 
                v-if="task.isCompleted || getTaskProgress(task) >= 100"
                type="primary" 
                size="small"
                @click="completeTask(task.id)"
              >
                完成任务
              </a-button>
              <a-button 
                v-else
                danger 
                size="small"
                @click="cancelTask(task.id)"
              >
                取消任务
              </a-button>
            </div>
          </a-card>
        </div>
      </div>
    </div>

    <!-- 派遣新任务 -->
    <div v-if="workshopStatus.unlocked" class="dispatch-section">
      <a-divider>派遣角色</a-divider>
      
      <!-- 角色选择 -->
      <div class="character-selection">
        <a-select
          v-model:value="selectedCharacters"
          mode="multiple"
          placeholder="选择要派遣的角色"
          style="width: 100%"
          :max-tag-count="3"
        >
          <a-select-option 
            v-for="character in availableCharacters" 
            :key="character.id"
            :value="character.id"
          >
            {{ character.name }} (Lv.{{ character.level }})
          </a-select-option>
        </a-select>
      </div>

      <!-- 材料需求 -->
      <div v-if="workshopConfig.requirements.length > 0" class="requirements">
        <div class="requirements-title">所需材料:</div>
        <div class="requirements-list">
          <a-tag 
            v-for="req in workshopConfig.requirements" 
            :key="req.itemId"
            :color="hasEnoughMaterial(req) ? 'green' : 'red'"
          >
            {{ getItemName(req.itemId) }} x{{ req.quantity }}
            (拥有: {{ getItemQuantity(req.itemId) }})
          </a-tag>
        </div>
      </div>

      <!-- 预期奖励 -->
      <div class="rewards-preview">
        <div class="rewards-title">预期奖励:</div>
        <div class="rewards-list">
          <a-tag v-if="workshopConfig.rewards.money" color="gold">
            灵石 +{{ workshopConfig.rewards.money }}
          </a-tag>
          <a-tag v-if="workshopConfig.rewards.exp" color="blue">
            经验 +{{ workshopConfig.rewards.exp }}
          </a-tag>
          <a-tag 
            v-for="item in workshopConfig.rewards.items || []" 
            :key="item.itemId"
            color="green"
          >
            {{ getItemName(item.itemId) }} +{{ item.quantity }}
          </a-tag>
        </div>
      </div>

      <!-- 派遣按钮 -->
      <div class="dispatch-actions">
        <a-button 
          type="primary" 
          :disabled="!canDispatchSelected"
          @click="handleDispatch"
          block
        >
          开始派遣 ({{ formatDuration(workshopConfig.duration) }})
        </a-button>
      </div>
    </div>

    <!-- 解锁提示 -->
    <div v-else class="unlock-hint">
      <a-empty description="此工坊尚未解锁">
        <template #image>
          <LockOutlined style="font-size: 48px; color: #ccc;" />
        </template>
      </a-empty>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { LockOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import type { WorkshopType, DispatchTask } from '@/types/game'
import { dispatchStore } from '@/stores/dispatchStore'
import { gameStore } from '@/stores/gameStore'
import { WORKSHOP_CONFIG } from '@/constants/game'

interface Props {
  workshopType: WorkshopType
}

const props = defineProps<Props>()

// 选中的角色
const selectedCharacters = ref<string[]>([])

// 工坊配置
const workshopConfig = computed(() => WORKSHOP_CONFIG[props.workshopType])

// 工坊状态
const workshopStatus = computed(() => dispatchStore.getWorkshopStatus(props.workshopType))

// 可用角色
const availableCharacters = computed(() => {
  return gameStore.characters.value.filter(char => 
    !char.isWorking && !dispatchStore.workingCharacters.value.has(char.id)
  )
})

// 是否可以派遣选中的角色
const canDispatchSelected = computed(() => {
  return selectedCharacters.value.length > 0 && 
         dispatchStore.canDispatch(props.workshopType, selectedCharacters.value)
})

// 获取任务角色名称
const getTaskCharacterNames = (task: DispatchTask): string => {
  const characters = task.characterIds.map(id => {
    const char = gameStore.characters.value.find(c => c.id === id)
    return char ? char.name : '未知'
  })
  return characters.join(', ')
}

// 获取任务状态颜色
const getTaskStatusColor = (task: DispatchTask): string => {
  if (task.isCompleted) return 'success'
  if (getTaskProgress(task) >= 100) return 'warning'
  return 'processing'
}

// 获取任务状态文本
const getTaskStatusText = (task: DispatchTask): string => {
  if (task.isCompleted) return '已完成'
  if (getTaskProgress(task) >= 100) return '可完成'
  return '进行中'
}

// 获取任务进度
const getTaskProgress = (task: DispatchTask): number => {
  return dispatchStore.getTaskProgress(task)
}

// 获取任务剩余时间
const getTaskRemainingTime = (task: DispatchTask): number => {
  return dispatchStore.getTaskRemainingTime(task)
}

// 格式化剩余时间
const formatRemainingTime = (milliseconds: number): string => {
  return dispatchStore.formatRemainingTime(milliseconds)
}

// 格式化持续时间
const formatDuration = (milliseconds: number): string => {
  const minutes = Math.floor(milliseconds / (1000 * 60))
  return `${minutes}分钟`
}

// 检查是否有足够材料
const hasEnoughMaterial = (requirement: { itemId: string; quantity: number }): boolean => {
  return gameStore.getItemQuantity(requirement.itemId) >= requirement.quantity
}

// 获取物品数量
const getItemQuantity = (itemId: string): number => {
  return gameStore.getItemQuantity(itemId)
}

// 获取物品名称
const getItemName = (itemId: string): string => {
  // 简化版本，实际应该从物品配置中获取
  const names: Record<string, string> = {
    'spring_water': '清泉水',
    'flower_honey': '花蜜',
    'spirit_fruit': '灵果',
    'rare_herb': '珍稀草药',
    'holy_water': '圣水',
    'honey_wine': '蜜酿',
    'lotion': '乳液',
    'nectar': '琼浆'
  }
  return names[itemId] || itemId
}

// 处理派遣
const handleDispatch = () => {
  if (!canDispatchSelected.value) {
    message.error('无法派遣选中的角色')
    return
  }

  const success = dispatchStore.dispatchCharacters(props.workshopType, selectedCharacters.value)
  if (success) {
    message.success('派遣成功！')
    selectedCharacters.value = []
  } else {
    message.error('派遣失败')
  }
}

// 完成任务
const completeTask = (taskId: string) => {
  const success = dispatchStore.completeTask(taskId)
  if (success) {
    message.success('任务完成！')
  } else {
    message.error('无法完成任务')
  }
}

// 取消任务
const cancelTask = (taskId: string) => {
  const success = dispatchStore.cancelTask(taskId)
  if (success) {
    message.info('任务已取消')
  } else {
    message.error('无法取消任务')
  }
}
</script>

<style scoped>
.workshop-panel {
  margin-bottom: 16px;
}

.workshop-status {
  margin-bottom: 16px;
}

.active-tasks {
  margin-bottom: 16px;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item {
  width: 100%;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-characters {
  font-weight: bold;
}

.task-progress {
  margin-bottom: 8px;
}

.task-time {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.task-actions {
  text-align: right;
}

.dispatch-section {
  margin-top: 16px;
}

.character-selection {
  margin-bottom: 16px;
}

.requirements,
.rewards-preview {
  margin-bottom: 16px;
}

.requirements-title,
.rewards-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.requirements-list,
.rewards-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.dispatch-actions {
  margin-top: 16px;
}

.unlock-hint {
  text-align: center;
  padding: 32px;
}
</style>
