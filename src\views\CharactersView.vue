<template>
  <div class="characters-view">
    <a-card title="角色列表" class="characters-card">
      <template #extra>
        <a-space>
          <a-select v-model:value="filterType" style="width: 120px">
            <a-select-option value="all">全部</a-select-option>
            <a-select-option value="normal">普通</a-select-option>
            <a-select-option value="rare">稀有</a-select-option>
            <a-select-option value="special">特殊</a-select-option>
          </a-select>
          <a-dropdown>
            <a-button type="primary">
              <template #icon>
                <PlusOutlined />
              </template>
              招募角色
            </a-button>
            <template #overlay>
              <a-menu @click="handleMenuClick">
                <a-menu-item key="normal">招募普通角色</a-menu-item>
                <a-menu-item key="rare">招募稀有角色</a-menu-item>
                <a-menu-divider />
                <a-menu-item key="garden">前往孤独园</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </template>

      <div class="characters-grid">
        <CharacterCard v-for="character in filteredCharacters" :key="character.id" :character="character"
          @click="showCharacterDetail" @view="showCharacterDetail" @level-up="levelUpCharacter"
          @add-exp="(char) => addExpToCharacter(char, 50)" />
      </div>

      <a-empty v-if="filteredCharacters.length === 0" description="暂无角色" />
    </a-card>

    <!-- 角色详情模态框 -->
    <a-modal v-model:open="detailVisible" :title="selectedCharacter?.name" width="600px" :footer="null">
      <div v-if="selectedCharacter" class="character-detail">
        <a-row gutter={16}>
          <a-col span={8}>
            <div class="detail-avatar" :class="`rarity-${selectedCharacter.rarity}`">
              <UserOutlined />
            </div>
          </a-col>
          <a-col span={16}>
            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="稀有度">
                <a-tag :color="getRarityColor(selectedCharacter.rarity)">
                  {{ getRarityText(selectedCharacter.rarity) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="等级">{{ selectedCharacter.level }}</a-descriptions-item>
              <a-descriptions-item label="经验">{{ selectedCharacter.exp }}/{{ selectedCharacter.maxExp
                }}</a-descriptions-item>
              <a-descriptions-item label="状态">
                <a-tag v-if="selectedCharacter.isWorking" color="blue">工作中</a-tag>
                <a-tag v-else color="green">空闲</a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
        </a-row>

        <a-divider>属性</a-divider>
        <a-row gutter={16}>
          <a-col span={6}>
            <a-statistic title="魅力" :value="selectedCharacter.attributes.charm" />
          </a-col>
          <a-col span={6}>
            <a-statistic title="技艺" :value="selectedCharacter.attributes.skill" />
          </a-col>
          <a-col span={6}>
            <a-statistic title="体力" :value="selectedCharacter.attributes.stamina" />
          </a-col>
          <a-col span={6}>
            <a-statistic title="智慧" :value="selectedCharacter.attributes.wisdom" />
          </a-col>
        </a-row>

        <div style="margin-top: 16px; text-align: center;">
          <a-space>
            <a-button v-if="selectedCharacter.exp >= selectedCharacter.maxExp" type="primary"
              @click="levelUpCharacter(selectedCharacter)">
              升级角色
            </a-button>
            <a-button @click="addExpToCharacter(selectedCharacter, 50)">
              增加经验 (+50)
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { UserOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { gameStore } from '@/stores/gameStore'
import type { Character } from '@/types/game'
import { Rarity } from '@/types/game'
import { getRarityColor, getRarityText } from '@/utils/gameData'
import { generateNormalCharacter, generateRareCharacter } from '@/utils/characterGenerator'
import CharacterCard from '@/components/CharacterCard.vue'

const filterType = ref('all')
const detailVisible = ref(false)
const selectedCharacter = ref<Character | null>(null)

// 从状态管理获取角色数据
const { characters } = gameStore

// 过滤后的角色列表
const filteredCharacters = computed(() => {
  if (filterType.value === 'all') {
    return characters.value
  }
  return characters.value.filter(char => char.rarity === filterType.value)
})



// 显示角色详情
const showCharacterDetail = (character: Character) => {
  selectedCharacter.value = character
  detailVisible.value = true
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'normal':
      recruitNormalCharacter()
      break
    case 'rare':
      recruitRareCharacter()
      break
    case 'garden':
      message.info('请前往孤独园招募新角色')
      break
  }
}

// 招募普通角色
const recruitNormalCharacter = () => {
  const newCharacter = generateNormalCharacter()
  gameStore.addCharacter(newCharacter)
  message.success(`成功招募到 ${newCharacter.name}！`)
}

// 招募稀有角色
const recruitRareCharacter = () => {
  const newCharacter = generateRareCharacter()
  gameStore.addCharacter(newCharacter)
  message.success(`成功招募到稀有角色 ${newCharacter.name}！`)
}

// 角色升级
const levelUpCharacter = (character: Character) => {
  if (character.exp >= character.maxExp) {
    character.level += 1
    character.exp -= character.maxExp
    character.maxExp = character.level * 100

    // 属性成长
    const growthRate = 1.1
    character.attributes.charm = Math.floor(character.attributes.charm * growthRate)
    character.attributes.skill = Math.floor(character.attributes.skill * growthRate)
    character.attributes.stamina = Math.floor(character.attributes.stamina * growthRate)
    character.attributes.wisdom = Math.floor(character.attributes.wisdom * growthRate)

    message.success(`${character.name} 升级了！`)
  } else {
    message.warning('经验不足，无法升级')
  }
}

// 给角色增加经验
const addExpToCharacter = (character: Character, exp: number) => {
  character.exp += exp
  message.success(`${character.name} 获得了 ${exp} 经验`)

  // 检查是否可以升级
  if (character.exp >= character.maxExp) {
    message.info(`${character.name} 可以升级了！`)
  }
}


</script>

<style scoped>
.characters-view {
  height: auto;
  padding: 16px;
}

.characters-card {
  height: 100%;
}

.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.character-card {
  transition: all 0.3s ease;
}

.character-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.character-avatar,
.detail-avatar {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.rarity-normal {
  background: linear-gradient(135deg, #a8a8a8 0%, #d3d3d3 100%);
}

.rarity-rare {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.rarity-special {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.character-stats {
  margin: 12px 0;
}

.character-status {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.character-detail .detail-avatar {
  height: 150px;
  border-radius: 8px;
}
</style>
