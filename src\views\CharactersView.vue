<template>
  <div class="characters-view">
    <a-card title="角色列表" class="characters-card">
      <template #extra>
        <a-space>
          <a-select v-model:value="filterType" style="width: 120px">
            <a-select-option value="all">全部</a-select-option>
            <a-select-option value="normal">普通</a-select-option>
            <a-select-option value="rare">稀有</a-select-option>
            <a-select-option value="special">特殊</a-select-option>
          </a-select>
          <a-button type="primary" @click="showAddCharacter">
            <template #icon><PlusOutlined /></template>
            招募角色
          </a-button>
        </a-space>
      </template>

      <div class="characters-grid">
        <a-card
          v-for="character in filteredCharacters"
          :key="character.id"
          hoverable
          class="character-card"
          @click="showCharacterDetail(character)"
        >
          <template #cover>
            <div class="character-avatar" :class="`rarity-${character.rarity}`">
              <UserOutlined />
            </div>
          </template>
          <a-card-meta :title="character.name" :description="character.description" />
          <div class="character-stats">
            <a-row gutter={8}>
              <a-col span={12}>
                <a-statistic title="等级" :value="character.level" />
              </a-col>
              <a-col span={12}>
                <a-statistic title="经验" :value="character.exp" :suffix="`/${character.maxExp}`" />
              </a-col>
            </a-row>
          </div>
          <div class="character-status">
            <a-tag v-if="character.isWorking" color="blue">工作中</a-tag>
            <a-tag v-else color="green">空闲</a-tag>
            <a-tag :color="getRarityColor(character.rarity)">{{ getRarityText(character.rarity) }}</a-tag>
          </div>
        </a-card>
      </div>

      <a-empty v-if="filteredCharacters.length === 0" description="暂无角色" />
    </a-card>

    <!-- 角色详情模态框 -->
    <a-modal
      v-model:open="detailVisible"
      :title="selectedCharacter?.name"
      width="600px"
      :footer="null"
    >
      <div v-if="selectedCharacter" class="character-detail">
        <a-row gutter={16}>
          <a-col span={8}>
            <div class="detail-avatar" :class="`rarity-${selectedCharacter.rarity}`">
              <UserOutlined />
            </div>
          </a-col>
          <a-col span={16}>
            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="稀有度">
                <a-tag :color="getRarityColor(selectedCharacter.rarity)">
                  {{ getRarityText(selectedCharacter.rarity) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="等级">{{ selectedCharacter.level }}</a-descriptions-item>
              <a-descriptions-item label="经验">{{ selectedCharacter.exp }}/{{ selectedCharacter.maxExp }}</a-descriptions-item>
              <a-descriptions-item label="状态">
                <a-tag v-if="selectedCharacter.isWorking" color="blue">工作中</a-tag>
                <a-tag v-else color="green">空闲</a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
        </a-row>
        
        <a-divider>属性</a-divider>
        <a-row gutter={16}>
          <a-col span={6}>
            <a-statistic title="魅力" :value="selectedCharacter.attributes.charm" />
          </a-col>
          <a-col span={6}>
            <a-statistic title="技艺" :value="selectedCharacter.attributes.skill" />
          </a-col>
          <a-col span={6}>
            <a-statistic title="体力" :value="selectedCharacter.attributes.stamina" />
          </a-col>
          <a-col span={6}>
            <a-statistic title="智慧" :value="selectedCharacter.attributes.wisdom" />
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { UserOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

interface Character {
  id: string
  name: string
  description: string
  rarity: 'normal' | 'rare' | 'special'
  level: number
  exp: number
  maxExp: number
  isWorking: boolean
  attributes: {
    charm: number
    skill: number
    stamina: number
    wisdom: number
  }
}

const filterType = ref('all')
const detailVisible = ref(false)
const selectedCharacter = ref<Character | null>(null)

// 模拟角色数据
const characters = ref<Character[]>([
  {
    id: '1',
    name: '小雅',
    description: '温柔善良的少女',
    rarity: 'normal',
    level: 1,
    exp: 0,
    maxExp: 100,
    isWorking: false,
    attributes: {
      charm: 15,
      skill: 12,
      stamina: 18,
      wisdom: 10
    }
  },
  {
    id: '2',
    name: '紫萱',
    description: '聪慧过人的才女',
    rarity: 'rare',
    level: 3,
    exp: 150,
    maxExp: 300,
    isWorking: true,
    attributes: {
      charm: 20,
      skill: 25,
      stamina: 15,
      wisdom: 22
    }
  }
])

// 过滤后的角色列表
const filteredCharacters = computed(() => {
  if (filterType.value === 'all') {
    return characters.value
  }
  return characters.value.filter(char => char.rarity === filterType.value)
})

// 获取稀有度颜色
const getRarityColor = (rarity: string) => {
  const colors = {
    normal: 'default',
    rare: 'blue',
    special: 'purple'
  }
  return colors[rarity as keyof typeof colors] || 'default'
}

// 获取稀有度文本
const getRarityText = (rarity: string) => {
  const texts = {
    normal: '普通',
    rare: '稀有',
    special: '特殊'
  }
  return texts[rarity as keyof typeof texts] || '未知'
}

// 显示角色详情
const showCharacterDetail = (character: Character) => {
  selectedCharacter.value = character
  detailVisible.value = true
}

// 显示招募角色
const showAddCharacter = () => {
  message.info('请前往孤独园招募新角色')
}

onMounted(() => {
  // TODO: 从状态管理中加载角色数据
})
</script>

<style scoped>
.characters-view {
  height: 100%;
  padding: 16px;
}

.characters-card {
  height: 100%;
}

.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.character-card {
  transition: all 0.3s ease;
}

.character-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.character-avatar, .detail-avatar {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.rarity-normal {
  background: linear-gradient(135deg, #a8a8a8 0%, #d3d3d3 100%);
}

.rarity-rare {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.rarity-special {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.character-stats {
  margin: 12px 0;
}

.character-status {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.character-detail .detail-avatar {
  height: 150px;
  border-radius: 8px;
}
</style>
