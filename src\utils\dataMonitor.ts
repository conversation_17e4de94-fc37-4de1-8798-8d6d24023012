import type { GameSave, Character, InventoryItem } from '@/types/game'

// 数据统计接口
export interface DataStatistics {
  totalCharacters: number
  charactersByRarity: Record<string, number>
  totalItems: number
  itemsByCategory: Record<string, number>
  totalValue: number
  averageCharacterLevel: number
  maxCharacterLevel: number
  storageUsage: number
  lastUpdated: number
}

// 性能监控接口
export interface PerformanceMetrics {
  saveTime: number
  loadTime: number
  dataSize: number
  operationCount: number
  errorCount: number
  lastOperation: string
  timestamp: number
}

// 数据监控器
export class DataMonitor {
  private static instance: DataMonitor
  private statistics: DataStatistics
  private performanceMetrics: PerformanceMetrics
  private operationHistory: Array<{ operation: string; timestamp: number; duration: number }> = []

  constructor() {
    this.statistics = this.createEmptyStatistics()
    this.performanceMetrics = this.createEmptyMetrics()
  }

  static getInstance(): DataMonitor {
    if (!DataMonitor.instance) {
      DataMonitor.instance = new DataMonitor()
    }
    return DataMonitor.instance
  }

  // 创建空统计数据
  private createEmptyStatistics(): DataStatistics {
    return {
      totalCharacters: 0,
      charactersByRarity: {},
      totalItems: 0,
      itemsByCategory: {},
      totalValue: 0,
      averageCharacterLevel: 0,
      maxCharacterLevel: 0,
      storageUsage: 0,
      lastUpdated: Date.now()
    }
  }

  // 创建空性能指标
  private createEmptyMetrics(): PerformanceMetrics {
    return {
      saveTime: 0,
      loadTime: 0,
      dataSize: 0,
      operationCount: 0,
      errorCount: 0,
      lastOperation: '',
      timestamp: Date.now()
    }
  }

  // 更新游戏数据统计
  updateGameStatistics(save: GameSave): void {
    const startTime = performance.now()

    try {
      this.statistics = {
        totalCharacters: save.characters.length,
        charactersByRarity: this.calculateCharactersByRarity(save.characters),
        totalItems: this.calculateTotalItems(save.inventory),
        itemsByCategory: this.calculateItemsByCategory(save.inventory),
        totalValue: this.calculateTotalValue(save.inventory),
        averageCharacterLevel: this.calculateAverageCharacterLevel(save.characters),
        maxCharacterLevel: this.calculateMaxCharacterLevel(save.characters),
        storageUsage: this.calculateDataSize(save),
        lastUpdated: Date.now()
      }

      const duration = performance.now() - startTime
      this.recordOperation('updateStatistics', duration)
    } catch (error) {
      this.performanceMetrics.errorCount++
      console.error('Failed to update statistics:', error)
    }
  }

  // 计算角色稀有度分布
  private calculateCharactersByRarity(characters: Character[]): Record<string, number> {
    const rarityCount: Record<string, number> = {}
    
    characters.forEach(character => {
      const rarity = character.rarity
      rarityCount[rarity] = (rarityCount[rarity] || 0) + 1
    })

    return rarityCount
  }

  // 计算物品总数
  private calculateTotalItems(inventory: InventoryItem[]): number {
    return inventory.reduce((total, item) => total + item.quantity, 0)
  }

  // 计算物品类别分布
  private calculateItemsByCategory(inventory: InventoryItem[]): Record<string, number> {
    const categoryCount: Record<string, number> = {}
    
    // 这里需要根据物品ID获取物品信息
    // 简化处理，直接按物品ID分类
    inventory.forEach(item => {
      const category = this.getItemCategory(item.itemId)
      categoryCount[category] = (categoryCount[category] || 0) + item.quantity
    })

    return categoryCount
  }

  // 获取物品类别（简化版本）
  private getItemCategory(itemId: string): string {
    if (itemId.includes('seed') || itemId.includes('fruit')) return 'material'
    if (itemId.includes('water') || itemId.includes('honey')) return 'product'
    if (itemId.includes('nectar')) return 'special'
    return 'other'
  }

  // 计算库存总价值
  private calculateTotalValue(inventory: InventoryItem[]): number {
    return inventory.reduce((total, item) => {
      const itemValue = this.getItemValue(item.itemId)
      return total + (itemValue * item.quantity)
    }, 0)
  }

  // 获取物品价值（简化版本）
  private getItemValue(itemId: string): number {
    const values: Record<string, number> = {
      'seed': 10,
      'spring_water': 20,
      'flower_honey': 30,
      'spirit_fruit': 50,
      'holy_water': 80,
      'honey_wine': 120,
      'lotion': 200,
      'nectar': 500
    }
    return values[itemId] || 10
  }

  // 计算角色平均等级
  private calculateAverageCharacterLevel(characters: Character[]): number {
    if (characters.length === 0) return 0
    
    const totalLevel = characters.reduce((sum, char) => sum + char.level, 0)
    return Math.round((totalLevel / characters.length) * 10) / 10
  }

  // 计算最高角色等级
  private calculateMaxCharacterLevel(characters: Character[]): number {
    if (characters.length === 0) return 0
    
    return Math.max(...characters.map(char => char.level))
  }

  // 计算数据大小
  private calculateDataSize(save: GameSave): number {
    return JSON.stringify(save).length
  }

  // 记录操作
  private recordOperation(operation: string, duration: number): void {
    this.performanceMetrics.operationCount++
    this.performanceMetrics.lastOperation = operation
    this.performanceMetrics.timestamp = Date.now()

    // 记录操作历史
    this.operationHistory.push({
      operation,
      timestamp: Date.now(),
      duration
    })

    // 保持历史记录在合理范围内
    if (this.operationHistory.length > 100) {
      this.operationHistory = this.operationHistory.slice(-50)
    }

    // 更新性能指标
    if (operation === 'save') {
      this.performanceMetrics.saveTime = duration
    } else if (operation === 'load') {
      this.performanceMetrics.loadTime = duration
    }
  }

  // 记录保存操作
  recordSaveOperation(duration: number, dataSize: number): void {
    this.performanceMetrics.dataSize = dataSize
    this.recordOperation('save', duration)
  }

  // 记录加载操作
  recordLoadOperation(duration: number): void {
    this.recordOperation('load', duration)
  }

  // 记录错误
  recordError(operation: string, error: Error): void {
    this.performanceMetrics.errorCount++
    console.error(`Data operation error [${operation}]:`, error)
  }

  // 获取统计数据
  getStatistics(): DataStatistics {
    return { ...this.statistics }
  }

  // 获取性能指标
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics }
  }

  // 获取操作历史
  getOperationHistory(): Array<{ operation: string; timestamp: number; duration: number }> {
    return [...this.operationHistory]
  }

  // 生成数据报告
  generateReport(): {
    statistics: DataStatistics
    performance: PerformanceMetrics
    recommendations: string[]
  } {
    const recommendations: string[] = []

    // 性能建议
    if (this.performanceMetrics.saveTime > 1000) {
      recommendations.push('保存时间较长，建议优化数据结构或减少存档频率')
    }

    if (this.performanceMetrics.errorCount > 5) {
      recommendations.push('错误次数较多，建议检查数据完整性')
    }

    if (this.statistics.storageUsage > 1024 * 1024) {
      recommendations.push('存档文件较大，建议清理不必要的数据')
    }

    // 游戏建议
    if (this.statistics.totalCharacters > 50) {
      recommendations.push('角色数量较多，建议整理角色列表')
    }

    if (this.statistics.totalItems > 1000) {
      recommendations.push('物品数量较多，建议出售不需要的物品')
    }

    return {
      statistics: this.getStatistics(),
      performance: this.getPerformanceMetrics(),
      recommendations
    }
  }

  // 重置统计数据
  reset(): void {
    this.statistics = this.createEmptyStatistics()
    this.performanceMetrics = this.createEmptyMetrics()
    this.operationHistory = []
  }

  // 导出监控数据
  exportData(): string {
    return JSON.stringify({
      statistics: this.statistics,
      performance: this.performanceMetrics,
      history: this.operationHistory
    }, null, 2)
  }
}

// 创建全局实例
export const dataMonitor = DataMonitor.getInstance()

// 数据监控装饰器
export function monitorDataOperation(operationName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const startTime = performance.now()
      
      try {
        const result = await method.apply(this, args)
        const duration = performance.now() - startTime
        dataMonitor.recordOperation(operationName, duration)
        return result
      } catch (error) {
        dataMonitor.recordError(operationName, error as Error)
        throw error
      }
    }

    return descriptor
  }
}
