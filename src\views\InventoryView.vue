<template>
  <div class="inventory-view">
    <a-card title="库存管理" class="inventory-card">
      <template #extra>
        <a-space>
          <a-select v-model:value="filterCategory" style="width: 120px">
            <a-select-option value="all">全部</a-select-option>
            <a-select-option value="materials">材料</a-select-option>
            <a-select-option value="products">商品</a-select-option>
            <a-select-option value="special">特殊</a-select-option>
          </a-select>
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索物品"
            style="width: 200px"
          />
        </a-space>
      </template>

      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="grid" tab="网格视图">
          <div class="inventory-grid">
            <a-card
              v-for="item in filteredItems"
              :key="item.id"
              hoverable
              class="item-card"
              @click="showItemDetail(item)"
            >
              <template #cover>
                <div class="item-icon" :class="`category-${item.category}`">
                  <component :is="getItemIcon(item.category)" />
                </div>
              </template>
              <a-card-meta :title="item.name" :description="item.description" />
              <div class="item-info">
                <a-statistic title="数量" :value="item.quantity" />
                <a-tag :color="getCategoryColor(item.category)">{{ getCategoryText(item.category) }}</a-tag>
              </div>
            </a-card>
          </div>
        </a-tab-pane>

        <a-tab-pane key="list" tab="列表视图">
          <a-table
            :columns="tableColumns"
            :data-source="filteredItems"
            :pagination="{ pageSize: 10 }"
            row-key="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'category'">
                <a-tag :color="getCategoryColor(record.category)">
                  {{ getCategoryText(record.category) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'actions'">
                <a-space>
                  <a-button size="small" @click="showItemDetail(record)">详情</a-button>
                  <a-button size="small" type="primary" @click="useItem(record)">使用</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>

      <a-empty v-if="filteredItems.length === 0" description="暂无物品" />
    </a-card>

    <!-- 物品详情模态框 -->
    <a-modal
      v-model:open="detailVisible"
      :title="selectedItem?.name"
      width="500px"
      :footer="null"
    >
      <div v-if="selectedItem" class="item-detail">
        <a-row gutter={16}>
          <a-col span={8}>
            <div class="detail-icon" :class="`category-${selectedItem.category}`">
              <component :is="getItemIcon(selectedItem.category)" />
            </div>
          </a-col>
          <a-col span={16}>
            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="类别">
                <a-tag :color="getCategoryColor(selectedItem.category)">
                  {{ getCategoryText(selectedItem.category) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="数量">{{ selectedItem.quantity }}</a-descriptions-item>
              <a-descriptions-item label="价值">{{ selectedItem.value }} 灵石</a-descriptions-item>
              <a-descriptions-item label="描述">{{ selectedItem.description }}</a-descriptions-item>
            </a-descriptions>
          </a-col>
        </a-row>
        
        <div class="item-actions" style="margin-top: 16px; text-align: center;">
          <a-space>
            <a-button type="primary" @click="useItem(selectedItem)">使用</a-button>
            <a-button @click="sellItem(selectedItem)">出售</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  GiftOutlined, 
  ExperimentOutlined, 
  StarOutlined,
  ShoppingOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

interface InventoryItem {
  id: string
  name: string
  description: string
  category: 'materials' | 'products' | 'special'
  quantity: number
  value: number
}

const filterCategory = ref('all')
const searchText = ref('')
const activeTab = ref('grid')
const detailVisible = ref(false)
const selectedItem = ref<InventoryItem | null>(null)

// 模拟库存数据
const inventory = ref<InventoryItem[]>([
  {
    id: '1',
    name: '灵果种子',
    description: '可以种植的神奇种子',
    category: 'materials',
    quantity: 10,
    value: 5
  },
  {
    id: '2',
    name: '圣水',
    description: '纯净的圣水，可用于浇灌',
    category: 'products',
    quantity: 5,
    value: 20
  },
  {
    id: '3',
    name: '蜜酿',
    description: '香甜的蜜酿，深受欢迎',
    category: 'products',
    quantity: 3,
    value: 50
  },
  {
    id: '4',
    name: '神秘宝石',
    description: '散发着神秘光芒的宝石',
    category: 'special',
    quantity: 1,
    value: 500
  }
])

// 表格列配置
const tableColumns = [
  { title: '名称', dataIndex: 'name', key: 'name' },
  { title: '类别', dataIndex: 'category', key: 'category' },
  { title: '数量', dataIndex: 'quantity', key: 'quantity' },
  { title: '价值', dataIndex: 'value', key: 'value' },
  { title: '操作', key: 'actions' }
]

// 过滤后的物品列表
const filteredItems = computed(() => {
  let items = inventory.value

  // 按类别过滤
  if (filterCategory.value !== 'all') {
    items = items.filter(item => item.category === filterCategory.value)
  }

  // 按搜索文本过滤
  if (searchText.value) {
    items = items.filter(item => 
      item.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
      item.description.toLowerCase().includes(searchText.value.toLowerCase())
    )
  }

  return items
})

// 获取物品图标
const getItemIcon = (category: string) => {
  const icons = {
    materials: ExperimentOutlined,
    products: GiftOutlined,
    special: StarOutlined
  }
  return icons[category as keyof typeof icons] || ShoppingOutlined
}

// 获取类别颜色
const getCategoryColor = (category: string) => {
  const colors = {
    materials: 'blue',
    products: 'green',
    special: 'purple'
  }
  return colors[category as keyof typeof colors] || 'default'
}

// 获取类别文本
const getCategoryText = (category: string) => {
  const texts = {
    materials: '材料',
    products: '商品',
    special: '特殊'
  }
  return texts[category as keyof typeof texts] || '未知'
}

// 显示物品详情
const showItemDetail = (item: InventoryItem) => {
  selectedItem.value = item
  detailVisible.value = true
}

// 使用物品
const useItem = (item: InventoryItem) => {
  if (item.quantity > 0) {
    item.quantity--
    message.success(`使用了 ${item.name}`)
  } else {
    message.warning('物品数量不足')
  }
  detailVisible.value = false
}

// 出售物品
const sellItem = (item: InventoryItem) => {
  if (item.quantity > 0) {
    item.quantity--
    message.success(`出售了 ${item.name}，获得 ${item.value} 灵石`)
  } else {
    message.warning('物品数量不足')
  }
  detailVisible.value = false
}

onMounted(() => {
  // TODO: 从状态管理中加载库存数据
})
</script>

<style scoped>
.inventory-view {
  height: 100%;
  padding: 16px;
}

.inventory-card {
  height: 100%;
}

.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.item-card {
  transition: all 0.3s ease;
}

.item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.item-icon, .detail-icon {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.category-materials {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.category-products {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.category-special {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.item-info {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-icon {
  height: 120px;
  border-radius: 8px;
}
</style>
