<template>
  <a-layout class="game-layout">
    <!-- 游戏头部 -->
    <a-layout-header class="game-header">
      <div class="header-content">
        <div class="game-title">琉璃胭脂坊</div>
        <div class="game-status">
          <a-space>
            <a-tag color="blue">{{ currentTimeDisplay }}</a-tag>
            <a-tag color="green">体力: {{ playerStatus.energy }}/{{ playerStatus.maxEnergy }}</a-tag>
            <a-tag color="gold">灵石: {{ playerStatus.money }}</a-tag>
          </a-space>
        </div>
      </div>
    </a-layout-header>

    <!-- 游戏主体内容 -->
    <a-layout-content class="game-content">
      <router-view />
    </a-layout-content>

    <!-- 游戏底部导航 -->
    <a-layout-footer class="game-footer">
      <a-row justify="space-around">
        <a-col>
          <a-button type="text" @click="goToMap" :class="{ active: currentRoute === 'map' }">
            <template #icon>
              <HomeOutlined />
            </template>
            地图
          </a-button>
        </a-col>
        <a-col>
          <a-button type="text" @click="goToCharacters" :class="{ active: currentRoute === 'characters' }">
            <template #icon>
              <UserOutlined />
            </template>
            角色
          </a-button>
        </a-col>
        <a-col>
          <a-button type="text" @click="goToInventory" :class="{ active: currentRoute === 'inventory' }">
            <template #icon>
              <ShoppingOutlined />
            </template>
            库存
          </a-button>
        </a-col>
        <a-col>
          <a-button type="text" @click="goToCalendar" :class="{ active: currentRoute === 'calendar' }">
            <template #icon>
              <CalendarOutlined />
            </template>
            日历
          </a-button>
        </a-col>
        <a-col>
          <a-button type="text" @click="goToTree" :class="{ active: currentRoute === 'tree' }">
            <template #icon>
              <BranchesOutlined />
            </template>
            发展树
          </a-button>
        </a-col>
      </a-row>
    </a-layout-footer>
  </a-layout>

  <!-- 新手引导 -->
  <TutorialGuide ref="tutorialRef" />
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  HomeOutlined,
  UserOutlined,
  ShoppingOutlined,
  CalendarOutlined,
  BranchesOutlined
} from '@ant-design/icons-vue'
import { gameStore } from '@/stores/gameStore'
import TutorialGuide from '@/components/TutorialGuide.vue'

const router = useRouter()
const route = useRoute()

// 新手引导引用
const tutorialRef = ref()

// 当前路由
const currentRoute = computed(() => route.name)

// 从状态管理获取玩家状态
const { playerStatus, currentTimeDisplay } = gameStore

// 导航方法
const goToMap = () => router.push({ name: 'map' })
const goToCharacters = () => router.push({ name: 'characters' })
const goToInventory = () => router.push({ name: 'inventory' })
const goToCalendar = () => router.push({ name: 'calendar' })
const goToTree = () => router.push({ name: 'tree' })
</script>

<style scoped>
.game-layout {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.game-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0 16px;
  height: 64px;
  line-height: 64px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.game-title {
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
}

.game-content {
  padding: 16px;
  overflow-y: auto;
  height: auto;
  background: rgba(255, 255, 255, 0.1);
}

.game-footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  height: auto;
}

.game-footer .ant-btn {
  height: 48px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
  border: none;
  box-shadow: none;
}

.game-footer .ant-btn.active {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.game-footer .ant-btn:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.05);
}
</style>
