import type { GameSave } from '@/types/game'
import { DB_NAME, DB_VERSION } from '@/constants/game'

// IndexedDB 数据库服务类
export class DatabaseService {
  private db: IDBDatabase | null = null
  private readonly dbName = DB_NAME
  private readonly dbVersion = DB_VERSION

  // 初始化数据库
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion)

      request.onerror = () => {
        reject(new Error('Failed to open database'))
      }

      request.onsuccess = () => {
        this.db = request.result
        resolve()
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result

        // 创建存档表
        if (!db.objectStoreNames.contains('saves')) {
          const saveStore = db.createObjectStore('saves', { keyPath: 'id' })
          saveStore.createIndex('createdAt', 'createdAt', { unique: false })
          saveStore.createIndex('lastSaved', 'lastSaved', { unique: false })
        }

        // 创建设置表
        if (!db.objectStoreNames.contains('settings')) {
          db.createObjectStore('settings', { keyPath: 'key' })
        }

        // 创建统计表
        if (!db.objectStoreNames.contains('statistics')) {
          db.createObjectStore('statistics', { keyPath: 'key' })
        }
      }
    })
  }

  // 确保数据库已初始化
  private ensureDB(): IDBDatabase {
    if (!this.db) {
      throw new Error('Database not initialized. Call init() first.')
    }
    return this.db
  }

  // 保存游戏存档
  async saveGame(gameSave: GameSave): Promise<void> {
    const db = this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['saves'], 'readwrite')
      const store = transaction.objectStore('saves')

      // 更新最后保存时间
      const saveData = {
        ...gameSave,
        lastSaved: Date.now()
      }

      const request = store.put(saveData)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error('Failed to save game'))
    })
  }

  // 加载游戏存档
  async loadGame(saveId: string): Promise<GameSave | null> {
    const db = this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['saves'], 'readonly')
      const store = transaction.objectStore('saves')
      const request = store.get(saveId)

      request.onsuccess = () => {
        resolve(request.result || null)
      }

      request.onerror = () => reject(new Error('Failed to load game'))
    })
  }

  // 获取所有存档列表
  async getAllSaves(): Promise<GameSave[]> {
    const db = this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['saves'], 'readonly')
      const store = transaction.objectStore('saves')
      const index = store.index('lastSaved')
      const request = index.getAll()

      request.onsuccess = () => {
        // 按最后保存时间倒序排列
        const saves = request.result.sort((a, b) => b.lastSaved - a.lastSaved)
        resolve(saves)
      }

      request.onerror = () => reject(new Error('Failed to get saves'))
    })
  }

  // 删除存档
  async deleteSave(saveId: string): Promise<void> {
    const db = this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['saves'], 'readwrite')
      const store = transaction.objectStore('saves')
      const request = store.delete(saveId)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error('Failed to delete save'))
    })
  }

  // 检查是否有存档
  async hasSaves(): Promise<boolean> {
    const saves = await this.getAllSaves()
    return saves.length > 0
  }

  // 获取最新存档
  async getLatestSave(): Promise<GameSave | null> {
    const saves = await this.getAllSaves()
    return saves.length > 0 ? saves[0] : null
  }

  // 保存设置
  async saveSetting(key: string, value: any): Promise<void> {
    const db = this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['settings'], 'readwrite')
      const store = transaction.objectStore('settings')
      const request = store.put({ key, value, updatedAt: Date.now() })

      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error('Failed to save setting'))
    })
  }

  // 加载设置
  async loadSetting(key: string): Promise<any> {
    const db = this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['settings'], 'readonly')
      const store = transaction.objectStore('settings')
      const request = store.get(key)

      request.onsuccess = () => {
        const result = request.result
        resolve(result ? result.value : null)
      }

      request.onerror = () => reject(new Error('Failed to load setting'))
    })
  }

  // 保存统计数据
  async saveStatistic(key: string, value: any): Promise<void> {
    const db = this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['statistics'], 'readwrite')
      const store = transaction.objectStore('statistics')
      const request = store.put({ key, value, updatedAt: Date.now() })

      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error('Failed to save statistic'))
    })
  }

  // 加载统计数据
  async loadStatistic(key: string): Promise<any> {
    const db = this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['statistics'], 'readonly')
      const store = transaction.objectStore('statistics')
      const request = store.get(key)

      request.onsuccess = () => {
        const result = request.result
        resolve(result ? result.value : null)
      }

      request.onerror = () => reject(new Error('Failed to load statistic'))
    })
  }

  // 清空所有数据
  async clearAllData(): Promise<void> {
    const db = this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['saves', 'settings', 'statistics'], 'readwrite')

      let completed = 0
      const total = 3

      const checkComplete = () => {
        completed++
        if (completed === total) {
          resolve()
        }
      }

      const handleError = () => {
        reject(new Error('Failed to clear data'))
      }

      // 清空存档
      const savesStore = transaction.objectStore('saves')
      const clearSaves = savesStore.clear()
      clearSaves.onsuccess = checkComplete
      clearSaves.onerror = handleError

      // 清空设置
      const settingsStore = transaction.objectStore('settings')
      const clearSettings = settingsStore.clear()
      clearSettings.onsuccess = checkComplete
      clearSettings.onerror = handleError

      // 清空统计
      const statisticsStore = transaction.objectStore('statistics')
      const clearStatistics = statisticsStore.clear()
      clearStatistics.onsuccess = checkComplete
      clearStatistics.onerror = handleError
    })
  }

  // 关闭数据库连接
  close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
    }
  }

  // 获取数据库大小估算
  async getDatabaseSize(): Promise<number> {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate()
      return estimate.usage || 0
    }
    return 0
  }

  // 检查浏览器是否支持IndexedDB
  static isSupported(): boolean {
    return 'indexedDB' in window
  }
}

// 创建单例实例
export const databaseService = new DatabaseService()
