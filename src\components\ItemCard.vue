<template>
  <a-card 
    :class="['item-card', `rarity-${item.rarity}`]"
    :hoverable="true"
    @click="$emit('click', item)"
  >
    <!-- 物品图标 -->
    <template #cover>
      <div class="item-icon">
        <img 
          :src="getIconUrl(item.iconId)" 
          :alt="item.name"
          @error="handleImageError"
        />
        <div class="rarity-badge">
          <a-tag :color="getRarityColor(item.rarity)">
            {{ getRarityText(item.rarity) }}
          </a-tag>
        </div>
        <div v-if="quantity > 0" class="quantity-badge">
          <a-badge :count="quantity" :overflow-count="999" />
        </div>
      </div>
    </template>

    <!-- 物品信息 -->
    <a-card-meta>
      <template #title>
        <div class="item-title">
          <span class="item-name">{{ item.name }}</span>
          <span v-if="showPrice" class="item-price">{{ item.basePrice }}灵石</span>
        </div>
      </template>
      
      <template #description>
        <div class="item-description">
          {{ item.description }}
        </div>
      </template>
    </a-card-meta>

    <!-- 物品属性 -->
    <div class="item-properties">
      <div class="property-row">
        <span class="property-label">类别:</span>
        <a-tag :color="getCategoryColor(item.category)">
          {{ getCategoryText(item.category) }}
        </a-tag>
      </div>
      
      <div v-if="item.stackable" class="property-row">
        <span class="property-label">可堆叠:</span>
        <span class="property-value">最大 {{ item.maxStack }}</span>
      </div>
      
      <div v-if="showPrice" class="property-row">
        <span class="property-label">售价:</span>
        <span class="property-value">{{ item.sellPrice }}灵石</span>
      </div>
    </div>

    <!-- 制作材料 -->
    <div v-if="item.craftingMaterials && item.craftingMaterials.length > 0" class="crafting-materials">
      <div class="materials-title">制作材料:</div>
      <div class="materials-list">
        <a-tag 
          v-for="material in item.craftingMaterials" 
          :key="material.id"
          size="small"
        >
          {{ material.id }} x{{ material.quantity }}
        </a-tag>
      </div>
    </div>

    <!-- 效果 -->
    <div v-if="item.effects && item.effects.length > 0" class="item-effects">
      <div class="effects-title">效果:</div>
      <div class="effects-list">
        <a-tag 
          v-for="effect in item.effects" 
          :key="effect"
          color="green"
          size="small"
        >
          {{ getEffectText(effect) }}
        </a-tag>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #actions>
      <a-tooltip title="查看详情">
        <EyeOutlined @click.stop="$emit('view', item)" />
      </a-tooltip>
      <a-tooltip title="使用物品" v-if="canUse">
        <PlayCircleOutlined @click.stop="$emit('use', item)" />
      </a-tooltip>
      <a-tooltip title="出售物品" v-if="canSell">
        <DollarOutlined @click.stop="$emit('sell', item)" />
      </a-tooltip>
    </template>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { EyeOutlined, PlayCircleOutlined, DollarOutlined } from '@ant-design/icons-vue'

// 扩展的物品接口
interface ExtendedItem {
  id: string
  name: string
  description: string
  category: string
  rarity: string
  iconId?: string
  stackable: boolean
  maxStack?: number
  basePrice: number
  sellPrice: number
  effects?: string[]
  craftingMaterials?: { id: string; quantity: number }[]
  unlockConditions?: string[]
}

interface Props {
  item: ExtendedItem
  quantity?: number
  showPrice?: boolean
  canUse?: boolean
  canSell?: boolean
}

interface Emits {
  (e: 'click', item: ExtendedItem): void
  (e: 'view', item: ExtendedItem): void
  (e: 'use', item: ExtendedItem): void
  (e: 'sell', item: ExtendedItem): void
}

const props = withDefaults(defineProps<Props>(), {
  quantity: 0,
  showPrice: true,
  canUse: false,
  canSell: true
})

const emit = defineEmits<Emits>()

// 获取图标URL
const getIconUrl = (iconId?: string): string => {
  if (!iconId) {
    return '/src/assets/graphics/icons/default.png'
  }
  return `/src/assets/graphics/icons/items/${iconId}.png`
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/src/assets/graphics/icons/default.png'
}

// 获取稀有度颜色
const getRarityColor = (rarity: string): string => {
  const colors = {
    common: 'default',
    uncommon: 'green',
    rare: 'blue',
    epic: 'purple',
    legendary: 'gold'
  }
  return colors[rarity as keyof typeof colors] || 'default'
}

// 获取稀有度文本
const getRarityText = (rarity: string): string => {
  const texts = {
    common: '普通',
    uncommon: '优秀',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说'
  }
  return texts[rarity as keyof typeof texts] || '未知'
}

// 获取类别颜色
const getCategoryColor = (category: string): string => {
  const colors = {
    material: 'cyan',
    product: 'blue',
    consumable: 'green',
    special: 'purple'
  }
  return colors[category as keyof typeof colors] || 'default'
}

// 获取类别文本
const getCategoryText = (category: string): string => {
  const texts = {
    material: '材料',
    product: '商品',
    consumable: '消耗品',
    special: '特殊'
  }
  return texts[category as keyof typeof texts] || '未知'
}

// 获取效果文本
const getEffectText = (effect: string): string => {
  const texts = {
    restore_energy_small: '恢复少量体力',
    boost_charm_temp: '临时提升魅力',
    boost_charm_permanent: '永久提升魅力',
    boost_all_attributes: '提升所有属性'
  }
  return texts[effect as keyof typeof texts] || effect
}
</script>

<style scoped>
.item-card {
  width: 240px;
  margin: 8px;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
}

.item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.item-card.rarity-common {
  border: 1px solid #d9d9d9;
}

.item-card.rarity-uncommon {
  border: 2px solid #52c41a;
  box-shadow: 0 0 8px rgba(82, 196, 26, 0.3);
}

.item-card.rarity-rare {
  border: 2px solid #1890ff;
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.3);
}

.item-card.rarity-epic {
  border: 2px solid #722ed1;
  box-shadow: 0 0 8px rgba(114, 46, 209, 0.3);
}

.item-card.rarity-legendary {
  border: 2px solid #faad14;
  box-shadow: 0 0 8px rgba(250, 173, 20, 0.3);
}

.item-icon {
  position: relative;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
}

.item-icon img {
  width: 64px;
  height: 64px;
  object-fit: contain;
}

.rarity-badge {
  position: absolute;
  top: 8px;
  left: 8px;
}

.quantity-badge {
  position: absolute;
  top: 8px;
  right: 8px;
}

.item-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-name {
  font-weight: bold;
  font-size: 14px;
}

.item-price {
  color: #faad14;
  font-size: 12px;
  font-weight: bold;
}

.item-description {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-properties {
  margin: 8px 0;
}

.property-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.property-label {
  font-size: 12px;
  color: #666;
}

.property-value {
  font-size: 12px;
  color: #333;
}

.crafting-materials,
.item-effects {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.materials-title,
.effects-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.materials-list,
.effects-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .item-card {
    width: 100%;
    margin: 4px 0;
  }
  
  .item-icon {
    height: 80px;
  }
  
  .item-icon img {
    width: 48px;
    height: 48px;
  }
}
</style>
