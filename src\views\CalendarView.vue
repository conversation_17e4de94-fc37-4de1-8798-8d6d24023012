<template>
  <div class="calendar-view">
    <a-row :gutter="16" class="calendar-row">
      <!-- 日历部分 -->
      <a-col :span="16" class="calendar-col">
        <a-card class="calendar-card">
          <template #title>
            <div class="calendar-header">
              <span>游戏日历</span>
              <a-tag color="blue" class="selected-date-tag">
                {{ selectedDate.format('YYYY年M月D日') }}
              </a-tag>
            </div>
          </template>
          <a-calendar v-model:value="selectedDate" @select="onDateSelect" class="game-calendar">
            <template #dateCellRender="{ current }">
              <div class="calendar-cell">
                <div v-if="getDateEvents(current).length > 0" class="event-indicators">
                  <div v-for="event in getDateEvents(current).slice(0, 2)" :key="event.id" class="event-dot"
                    :style="{ backgroundColor: getEventColor(event.type) }" :title="event.title"></div>
                  <div v-if="getDateEvents(current).length > 2" class="event-more"
                    :title="`还有${getDateEvents(current).length - 2}个事件`">
                    +{{ getDateEvents(current).length - 2 }}
                  </div>
                </div>
              </div>
            </template>
          </a-calendar>
        </a-card>
      </a-col>

      <!-- 任务和事件部分 -->
      <a-col :span="8" class="sidebar-col">
        <a-card title="任务列表" class="tasks-card">
          <template #extra>
            <a-button type="primary" size="small" @click="showAddTask">
              <template #icon>
                <PlusOutlined />
              </template>
              添加任务
            </a-button>
          </template>

          <a-list :data-source="currentTasks" :locale="{ emptyText: '暂无任务' }">
            <template #renderItem="{ item }">
              <a-list-item>
                <template #actions>
                  <a-button size="small" @click="completeTask(item)">完成</a-button>
                </template>
                <a-list-item-meta>
                  <template #title>
                    <span :class="{ completed: item.completed }">{{ item.title }}</span>
                  </template>
                  <template #description>
                    <div>
                      <div>{{ item.description }}</div>
                      <a-tag :color="getTaskPriorityColor(item.priority)" size="small">
                        {{ getTaskPriorityText(item.priority) }}
                      </a-tag>
                      <a-tag v-if="item.deadline" color="orange" size="small">
                        截止: {{ formatDate(item.deadline) }}
                      </a-tag>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>

        <a-card :title="selectedDateEventsTitle" class="events-card" style="margin-top: 16px;">
          <template v-if="selectedDateEvents.length > 0">
            <a-timeline>
              <a-timeline-item v-for="event in selectedDateEvents" :key="event.id"
                :color="getEventTagColor(event.type)">
                <template #dot>
                  <component :is="getEventIcon(event.type)" />
                </template>
                <div class="event-item">
                  <div class="event-title">{{ event.title }}</div>
                  <div class="event-description">{{ event.description }}</div>
                  <div class="event-time">{{ event.time }}</div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </template>
          <template v-else>
            <a-empty description="该日期暂无事件" :image="false" />
          </template>
        </a-card>
      </a-col>
    </a-row>

    <!-- 添加任务模态框 -->
    <a-modal v-model:open="addTaskVisible" title="添加新任务" @ok="addTask" @cancel="resetTaskForm">
      <a-form :model="newTask" layout="vertical">
        <a-form-item label="任务标题" required>
          <a-input v-model:value="newTask.title" placeholder="请输入任务标题" />
        </a-form-item>
        <a-form-item label="任务描述">
          <a-textarea v-model:value="newTask.description" placeholder="请输入任务描述" />
        </a-form-item>
        <a-form-item label="优先级">
          <a-select v-model:value="newTask.priority">
            <a-select-option value="low">低</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="high">高</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="截止日期">
          <a-date-picker v-model:value="newTask.deadline" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  PlusOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs, { Dayjs } from 'dayjs'

interface Task {
  id: string
  title: string
  description: string
  priority: 'low' | 'medium' | 'high'
  completed: boolean
  deadline?: Dayjs
  createdAt: Dayjs
}

interface GameEvent {
  id: string
  title: string
  description: string
  type: 'story' | 'task' | 'special' | 'system'
  time: string
  date: Dayjs
}

const selectedDate = ref<Dayjs>(dayjs())
const addTaskVisible = ref(false)

// 任务数据
const tasks = ref<Task[]>([
  {
    id: '1',
    title: '招募新角色',
    description: '前往孤独园招募一名新的少女',
    priority: 'high',
    completed: false,
    deadline: dayjs().add(3, 'day'),
    createdAt: dayjs()
  },
  {
    id: '2',
    title: '收集材料',
    description: '收集10个灵果种子',
    priority: 'medium',
    completed: false,
    createdAt: dayjs()
  }
])

// 事件数据
const events = ref<GameEvent[]>([
  {
    id: '1',
    title: '新手引导完成',
    description: '恭喜完成新手引导！',
    type: 'story',
    time: '上午',
    date: dayjs()
  },
  {
    id: '2',
    title: '黑市拍卖会',
    description: '今天是周六，黑市拍卖会开放',
    type: 'special',
    time: '下午',
    date: dayjs().day(6)
  }
])

// 新任务表单
const newTask = ref({
  title: '',
  description: '',
  priority: 'medium' as 'low' | 'medium' | 'high',
  deadline: undefined as Dayjs | undefined
})

// 当前任务列表
const currentTasks = computed(() => {
  return tasks.value.filter(task => !task.completed)
})

// 今日事件
const todayEvents = computed(() => {
  return events.value.filter(event => event.date.isSame(dayjs(), 'day'))
})

// 选中日期的事件
const selectedDateEvents = computed(() => {
  return events.value.filter(event => event.date.isSame(selectedDate.value, 'day'))
})

// 选中日期事件标题
const selectedDateEventsTitle = computed(() => {
  const isToday = selectedDate.value.isSame(dayjs(), 'day')
  const dateStr = selectedDate.value.format('M月D日')
  return isToday ? '今日事件' : `${dateStr} 事件`
})

// 获取指定日期的事件
const getDateEvents = (date: Dayjs) => {
  return events.value.filter(event => event.date.isSame(date, 'day'))
}

// 获取事件颜色
const getEventColor = (type: string) => {
  const colors = {
    story: '#1890ff',
    task: '#52c41a',
    special: '#722ed1',
    system: '#fa8c16'
  }
  return colors[type as keyof typeof colors] || '#d9d9d9'
}

// 获取事件标签颜色（用于时间线等组件）
const getEventTagColor = (type: string) => {
  const colors = {
    story: 'blue',
    task: 'green',
    special: 'purple',
    system: 'orange'
  }
  return colors[type as keyof typeof colors] || 'default'
}

// 获取事件图标
const getEventIcon = (type: string) => {
  const icons = {
    story: CalendarOutlined,
    task: CheckCircleOutlined,
    special: ExclamationCircleOutlined,
    system: ClockCircleOutlined
  }
  return icons[type as keyof typeof icons] || CalendarOutlined
}

// 获取任务优先级颜色
const getTaskPriorityColor = (priority: string) => {
  const colors = {
    low: 'default',
    medium: 'blue',
    high: 'red'
  }
  return colors[priority as keyof typeof colors] || 'default'
}

// 获取任务优先级文本
const getTaskPriorityText = (priority: string) => {
  const texts = {
    low: '低优先级',
    medium: '中优先级',
    high: '高优先级'
  }
  return texts[priority as keyof typeof texts] || '未知'
}

// 格式化日期
const formatDate = (date: Dayjs) => {
  return date.format('MM-DD')
}

// 日期选择
const onDateSelect = (date: Dayjs) => {
  selectedDate.value = date
}

// 显示添加任务
const showAddTask = () => {
  addTaskVisible.value = true
}

// 添加任务
const addTask = () => {
  if (!newTask.value.title) {
    message.error('请输入任务标题')
    return
  }

  const task: Task = {
    id: Date.now().toString(),
    title: newTask.value.title,
    description: newTask.value.description,
    priority: newTask.value.priority,
    completed: false,
    deadline: newTask.value.deadline,
    createdAt: dayjs()
  }

  tasks.value.push(task)
  message.success('任务添加成功')
  addTaskVisible.value = false
  resetTaskForm()
}

// 重置任务表单
const resetTaskForm = () => {
  newTask.value = {
    title: '',
    description: '',
    priority: 'medium',
    deadline: undefined
  }
}

// 完成任务
const completeTask = (task: Task) => {
  task.completed = true
  message.success(`任务"${task.title}"已完成`)
}

onMounted(() => {
  // TODO: 从状态管理中加载任务和事件数据
})
</script>

<style scoped>
.calendar-view {
  height: auto;
  padding: 16px;
  overflow: hidden;
}

.calendar-row {
  height: 100%;
  margin: 0 !important;
}

.calendar-col {
  height: 100%;
  padding-right: 8px !important;
}

.sidebar-col {
  height: 100%;
  padding-left: 8px !important;
  overflow-y: auto;
}

.calendar-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.calendar-card :deep(.ant-card-body) {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.game-calendar {
  height: 100%;
}

.game-calendar :deep(.ant-picker-calendar) {
  height: 100%;
}

.game-calendar :deep(.ant-picker-calendar-header) {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.game-calendar :deep(.ant-picker-content) {
  height: calc(100% - 60px);
}

.game-calendar :deep(.ant-picker-calendar-date-content) {
  height: 60px;
  position: relative;
}

.tasks-card,
.events-card {
  height: fit-content;
  margin-bottom: 16px;
}

.calendar-cell {
  height: 100%;
  position: relative;
}

.event-indicators {
  position: absolute;
  bottom: 2px;
  left: 2px;
  right: 2px;
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  align-items: center;
}

.event-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.event-more {
  font-size: 10px;
  color: #666;
  background: #f0f0f0;
  border-radius: 2px;
  padding: 0 2px;
  line-height: 1;
}

.completed {
  text-decoration: line-through;
  color: #999;
}

.event-item {
  padding: 4px 0;
}

.event-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.event-description {
  color: #666;
  font-size: 12px;
  margin-bottom: 4px;
}

.event-time {
  color: #999;
  font-size: 11px;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.selected-date-tag {
  margin-left: 8px;
}

/* 日历单元格悬停效果 */
.game-calendar :deep(.ant-picker-cell) {
  transition: all 0.2s ease;
}

.game-calendar :deep(.ant-picker-cell:hover) {
  background-color: #f5f5f5;
}

.game-calendar :deep(.ant-picker-cell-selected) {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.game-calendar :deep(.ant-picker-cell-today) {
  border-color: #1890ff;
}

/* 事件指示器动画 */
.event-dot {
  transition: transform 0.2s ease;
}

.calendar-cell:hover .event-dot {
  transform: scale(1.2);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .calendar-row {
    flex-direction: column;
  }

  .calendar-col {
    width: 100% !important;
    max-width: 100% !important;
    flex: none !important;
    margin-bottom: 16px;
    padding-right: 0 !important;
  }

  .sidebar-col {
    width: 100% !important;
    max-width: 100% !important;
    flex: none !important;
    padding-left: 0 !important;
  }

  .calendar-card {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .calendar-view {
    padding: 8px;
  }

  .calendar-card {
    height: 400px;
  }

  .game-calendar :deep(.ant-picker-calendar-date-content) {
    height: 35px;
  }

  .event-indicators {
    bottom: 1px;
    left: 1px;
    right: 1px;
  }

  .event-dot {
    width: 4px;
    height: 4px;
  }

  .event-more {
    font-size: 8px;
  }
}

@media (max-width: 480px) {
  .calendar-view {
    padding: 4px;
  }

  .calendar-card {
    height: 350px;
  }

  .game-calendar :deep(.ant-picker-calendar-date-content) {
    height: 30px;
  }

  .tasks-card,
  .events-card {
    margin-bottom: 8px;
  }
}
</style>
