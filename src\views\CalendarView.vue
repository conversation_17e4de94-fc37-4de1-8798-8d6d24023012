<template>
  <div class="calendar-view">
    <a-row gutter={16}>
      <!-- 日历部分 -->
      <a-col span={16}>
        <a-card title="游戏日历" class="calendar-card">
          <a-calendar v-model:value="selectedDate" @select="onDateSelect">
            <template #dateCellRender="{ current }">
              <div class="calendar-cell">
                <div v-if="getDateEvents(current).length > 0" class="event-indicators">
                  <a-badge
                    v-for="event in getDateEvents(current).slice(0, 2)"
                    :key="event.id"
                    :color="getEventColor(event.type)"
                    :text="event.title"
                    class="event-badge"
                  />
                </div>
              </div>
            </template>
          </a-calendar>
        </a-card>
      </a-col>

      <!-- 任务和事件部分 -->
      <a-col span={8}>
        <a-card title="任务列表" class="tasks-card">
          <template #extra>
            <a-button type="primary" size="small" @click="showAddTask">
              <template #icon><PlusOutlined /></template>
              添加任务
            </a-button>
          </template>

          <a-list
            :data-source="currentTasks"
            :locale="{ emptyText: '暂无任务' }"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <template #actions>
                  <a-button size="small" @click="completeTask(item)">完成</a-button>
                </template>
                <a-list-item-meta>
                  <template #title>
                    <span :class="{ completed: item.completed }">{{ item.title }}</span>
                  </template>
                  <template #description>
                    <div>
                      <div>{{ item.description }}</div>
                      <a-tag :color="getTaskPriorityColor(item.priority)" size="small">
                        {{ getTaskPriorityText(item.priority) }}
                      </a-tag>
                      <a-tag v-if="item.deadline" color="orange" size="small">
                        截止: {{ formatDate(item.deadline) }}
                      </a-tag>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>

        <a-card title="今日事件" class="events-card" style="margin-top: 16px;">
          <a-timeline>
            <a-timeline-item
              v-for="event in todayEvents"
              :key="event.id"
              :color="getEventColor(event.type)"
            >
              <template #dot>
                <component :is="getEventIcon(event.type)" />
              </template>
              <div class="event-item">
                <div class="event-title">{{ event.title }}</div>
                <div class="event-description">{{ event.description }}</div>
                <div class="event-time">{{ event.time }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </a-col>
    </a-row>

    <!-- 添加任务模态框 -->
    <a-modal
      v-model:open="addTaskVisible"
      title="添加新任务"
      @ok="addTask"
      @cancel="resetTaskForm"
    >
      <a-form :model="newTask" layout="vertical">
        <a-form-item label="任务标题" required>
          <a-input v-model:value="newTask.title" placeholder="请输入任务标题" />
        </a-form-item>
        <a-form-item label="任务描述">
          <a-textarea v-model:value="newTask.description" placeholder="请输入任务描述" />
        </a-form-item>
        <a-form-item label="优先级">
          <a-select v-model:value="newTask.priority">
            <a-select-option value="low">低</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="high">高</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="截止日期">
          <a-date-picker v-model:value="newTask.deadline" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  PlusOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs, { Dayjs } from 'dayjs'

interface Task {
  id: string
  title: string
  description: string
  priority: 'low' | 'medium' | 'high'
  completed: boolean
  deadline?: Dayjs
  createdAt: Dayjs
}

interface GameEvent {
  id: string
  title: string
  description: string
  type: 'story' | 'task' | 'special' | 'system'
  time: string
  date: Dayjs
}

const selectedDate = ref<Dayjs>(dayjs())
const addTaskVisible = ref(false)

// 任务数据
const tasks = ref<Task[]>([
  {
    id: '1',
    title: '招募新角色',
    description: '前往孤独园招募一名新的少女',
    priority: 'high',
    completed: false,
    deadline: dayjs().add(3, 'day'),
    createdAt: dayjs()
  },
  {
    id: '2',
    title: '收集材料',
    description: '收集10个灵果种子',
    priority: 'medium',
    completed: false,
    createdAt: dayjs()
  }
])

// 事件数据
const events = ref<GameEvent[]>([
  {
    id: '1',
    title: '新手引导完成',
    description: '恭喜完成新手引导！',
    type: 'story',
    time: '上午',
    date: dayjs()
  },
  {
    id: '2',
    title: '黑市拍卖会',
    description: '今天是周六，黑市拍卖会开放',
    type: 'special',
    time: '下午',
    date: dayjs().day(6)
  }
])

// 新任务表单
const newTask = ref({
  title: '',
  description: '',
  priority: 'medium' as 'low' | 'medium' | 'high',
  deadline: undefined as Dayjs | undefined
})

// 当前任务列表
const currentTasks = computed(() => {
  return tasks.value.filter(task => !task.completed)
})

// 今日事件
const todayEvents = computed(() => {
  return events.value.filter(event => event.date.isSame(dayjs(), 'day'))
})

// 获取指定日期的事件
const getDateEvents = (date: Dayjs) => {
  return events.value.filter(event => event.date.isSame(date, 'day'))
}

// 获取事件颜色
const getEventColor = (type: string) => {
  const colors = {
    story: 'blue',
    task: 'green',
    special: 'purple',
    system: 'orange'
  }
  return colors[type as keyof typeof colors] || 'default'
}

// 获取事件图标
const getEventIcon = (type: string) => {
  const icons = {
    story: CalendarOutlined,
    task: CheckCircleOutlined,
    special: ExclamationCircleOutlined,
    system: ClockCircleOutlined
  }
  return icons[type as keyof typeof icons] || CalendarOutlined
}

// 获取任务优先级颜色
const getTaskPriorityColor = (priority: string) => {
  const colors = {
    low: 'default',
    medium: 'blue',
    high: 'red'
  }
  return colors[priority as keyof typeof colors] || 'default'
}

// 获取任务优先级文本
const getTaskPriorityText = (priority: string) => {
  const texts = {
    low: '低优先级',
    medium: '中优先级',
    high: '高优先级'
  }
  return texts[priority as keyof typeof texts] || '未知'
}

// 格式化日期
const formatDate = (date: Dayjs) => {
  return date.format('MM-DD')
}

// 日期选择
const onDateSelect = (date: Dayjs) => {
  selectedDate.value = date
}

// 显示添加任务
const showAddTask = () => {
  addTaskVisible.value = true
}

// 添加任务
const addTask = () => {
  if (!newTask.value.title) {
    message.error('请输入任务标题')
    return
  }

  const task: Task = {
    id: Date.now().toString(),
    title: newTask.value.title,
    description: newTask.value.description,
    priority: newTask.value.priority,
    completed: false,
    deadline: newTask.value.deadline,
    createdAt: dayjs()
  }

  tasks.value.push(task)
  message.success('任务添加成功')
  addTaskVisible.value = false
  resetTaskForm()
}

// 重置任务表单
const resetTaskForm = () => {
  newTask.value = {
    title: '',
    description: '',
    priority: 'medium',
    deadline: undefined
  }
}

// 完成任务
const completeTask = (task: Task) => {
  task.completed = true
  message.success(`任务"${task.title}"已完成`)
}

onMounted(() => {
  // TODO: 从状态管理中加载任务和事件数据
})
</script>

<style scoped>
.calendar-view {
  height: 100%;
  padding: 16px;
}

.calendar-card, .tasks-card, .events-card {
  height: fit-content;
}

.calendar-cell {
  height: 100%;
}

.event-indicators {
  margin-top: 4px;
}

.event-badge {
  display: block;
  margin-bottom: 2px;
  font-size: 10px;
}

.completed {
  text-decoration: line-through;
  color: #999;
}

.event-item {
  padding: 4px 0;
}

.event-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.event-description {
  color: #666;
  font-size: 12px;
  margin-bottom: 4px;
}

.event-time {
  color: #999;
  font-size: 11px;
}
</style>
