<template>
  <div class="hall-view">
    <a-card title="会客大厅" class="hall-card">
      <template #extra>
        <a-space>
          <a-tag color="blue">可用槽位: {{ workshopStatus.availableSlots }}/{{ workshopStatus.totalSlots }}</a-tag>
          <a-button @click="goBack">返回工坊</a-button>
        </a-space>
      </template>

      <div class="hall-content">
        <!-- 派遣区域 -->
        <a-card title="派遣角色" class="dispatch-section">
          <div class="character-selection">
            <h4>选择角色进行歌舞曲艺表演：</h4>
            <a-row gutter={16}>
              <a-col 
                v-for="character in availableCharacters" 
                :key="character.id" 
                span={6}
              >
                <a-card 
                  hoverable 
                  class="character-card"
                  :class="{ selected: selectedCharacters.includes(character.id) }"
                  @click="toggleCharacterSelection(character.id)"
                >
                  <template #cover>
                    <div class="character-avatar">
                      <UserOutlined />
                    </div>
                  </template>
                  <a-card-meta :title="character.name" :description="`等级 ${character.level}`" />
                  <div class="character-stats">
                    <a-tag color="pink">魅力: {{ character.attributes.charm }}</a-tag>
                    <a-tag color="blue">技艺: {{ character.attributes.skill }}</a-tag>
                  </div>
                </a-card>
              </a-col>
            </a-row>
            
            <div class="dispatch-actions" style="margin-top: 16px;">
              <a-space>
                <a-button 
                  type="primary" 
                  size="large"
                  @click="startDispatch"
                  :disabled="!canDispatch"
                  :loading="dispatching"
                >
                  开始表演 ({{ selectedCharacters.length }} 人)
                </a-button>
                <a-button @click="clearSelection">清空选择</a-button>
              </a-space>
            </div>
          </div>
        </a-card>

        <!-- 当前任务 -->
        <a-card title="进行中的表演" class="active-tasks-section" style="margin-top: 16px;">
          <div v-if="activeTasks.length === 0" class="empty-tasks">
            <a-empty description="暂无进行中的表演" />
          </div>
          <div v-else>
            <a-list
              :data-source="activeTasks"
              item-layout="horizontal"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <template #actions>
                    <a-button 
                      v-if="getTaskProgress(item) >= 100"
                      type="primary"
                      @click="completeTask(item.id)"
                    >
                      完成
                    </a-button>
                    <a-button 
                      v-else
                      danger
                      @click="cancelTask(item.id)"
                    >
                      取消
                    </a-button>
                  </template>
                  <a-list-item-meta>
                    <template #title>
                      <span>表演任务 - {{ item.characterIds.length }} 人</span>
                    </template>
                    <template #description>
                      <div>
                        <div>参与角色: {{ getCharacterNames(item.characterIds).join(', ') }}</div>
                        <div style="margin-top: 8px;">
                          <a-progress 
                            :percent="getTaskProgress(item)" 
                            :status="getTaskProgress(item) >= 100 ? 'success' : 'active'"
                          />
                        </div>
                        <div v-if="getTaskProgress(item) < 100" style="margin-top: 4px;">
                          剩余时间: {{ formatRemainingTime(getTaskRemainingTime(item)) }}
                        </div>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </div>
        </a-card>

        <!-- 奖励说明 -->
        <a-card title="表演奖励" class="rewards-section" style="margin-top: 16px;">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="基础收益">100 灵石</a-descriptions-item>
            <a-descriptions-item label="经验奖励">20 经验</a-descriptions-item>
            <a-descriptions-item label="表演时长">1 小时</a-descriptions-item>
            <a-descriptions-item label="消耗体力">1 点/人</a-descriptions-item>
          </a-descriptions>
          
          <div style="margin-top: 16px;">
            <h4>表演效果：</h4>
            <ul>
              <li>角色的魅力和技艺属性会影响收益</li>
              <li>多人表演会有额外的配合加成</li>
              <li>高等级角色表演效果更佳</li>
            </ul>
          </div>
        </a-card>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { UserOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { gameStore } from '@/stores/gameStore'
import { dispatchStore } from '@/stores/dispatchStore'
import { WorkshopType } from '@/types/game'

const router = useRouter()

// 选中的角色
const selectedCharacters = ref<string[]>([])
const dispatching = ref(false)

// 从状态管理获取数据
const { availableCharacters } = gameStore
const { 
  dispatchCharacters, 
  completeTask: completeDispatchTask, 
  cancelTask: cancelDispatchTask,
  getTaskProgress,
  getTaskRemainingTime,
  formatRemainingTime,
  getWorkshopStatus
} = dispatchStore

// 工坊状态
const workshopStatus = computed(() => getWorkshopStatus(WorkshopType.HALL))

// 当前活跃任务
const activeTasks = computed(() => workshopStatus.value.activeTasks)

// 是否可以派遣
const canDispatch = computed(() => {
  return selectedCharacters.value.length > 0 && 
         selectedCharacters.value.length <= workshopStatus.value.availableSlots
})

// 切换角色选择
const toggleCharacterSelection = (characterId: string) => {
  const index = selectedCharacters.value.indexOf(characterId)
  if (index > -1) {
    selectedCharacters.value.splice(index, 1)
  } else {
    if (selectedCharacters.value.length < workshopStatus.value.availableSlots) {
      selectedCharacters.value.push(characterId)
    } else {
      message.warning('已达到最大派遣数量')
    }
  }
}

// 清空选择
const clearSelection = () => {
  selectedCharacters.value = []
}

// 开始派遣
const startDispatch = async () => {
  if (!canDispatch.value) {
    message.error('无法开始表演')
    return
  }

  dispatching.value = true
  
  try {
    // 消耗角色体力
    for (const charId of selectedCharacters.value) {
      if (!gameStore.consumeEnergy(1)) {
        message.error('体力不足')
        return
      }
    }

    if (dispatchCharacters(WorkshopType.HALL, selectedCharacters.value)) {
      message.success('表演开始！')
      selectedCharacters.value = []
    } else {
      message.error('派遣失败')
    }
  } catch (error) {
    message.error('派遣失败')
    console.error(error)
  } finally {
    dispatching.value = false
  }
}

// 完成任务
const completeTask = (taskId: string) => {
  if (completeDispatchTask(taskId)) {
    message.success('表演完成，获得奖励！')
  } else {
    message.error('完成任务失败')
  }
}

// 取消任务
const cancelTask = (taskId: string) => {
  if (cancelDispatchTask(taskId)) {
    message.success('已取消表演')
  } else {
    message.error('取消任务失败')
  }
}

// 获取角色名称
const getCharacterNames = (characterIds: string[]): string[] => {
  return characterIds.map(id => {
    const character = gameStore.characters.value.find(c => c.id === id)
    return character ? character.name : '未知角色'
  })
}

const goBack = () => {
  router.push({ name: 'workshop' })
}
</script>

<style scoped>
.hall-view {
  height: 100%;
  padding: 16px;
}

.hall-card {
  height: 100%;
}

.hall-content {
  padding: 16px 0;
}

.character-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.character-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.character-card.selected {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.character-avatar {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.character-stats {
  margin-top: 8px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.dispatch-actions {
  text-align: center;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
}

.empty-tasks {
  text-align: center;
  padding: 40px;
}

.rewards-section ul {
  margin: 0;
  padding-left: 20px;
}

.rewards-section li {
  margin-bottom: 4px;
}
</style>
