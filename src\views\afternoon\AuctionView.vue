<template>
  <div class="auction-view">
    <div class="view-header">
      <a-page-header
        title="黑市拍卖会"
        sub-title="稀有物品竞拍"
        @back="goBack"
      >
        <template #extra>
          <a-space>
            <a-statistic title="当前金钱" :value="playerStatus.money" suffix="灵石" />
            <a-statistic title="当前时间" :value="currentTimeDisplay" />
          </a-space>
        </template>
      </a-page-header>
    </div>

    <div class="view-content">
      <BlackMarketAuction />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { gameStore } from '@/stores/gameStore'
import BlackMarketAuction from '@/components/BlackMarketAuction.vue'

const router = useRouter()

// 从状态管理获取游戏状态
const {
  playerStatus,
  currentTimeDisplay
} = gameStore

// 返回地图
const goBack = () => {
  router.push({ name: 'map' })
}
</script>

<style scoped>
.auction-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.view-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.view-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}
</style>
