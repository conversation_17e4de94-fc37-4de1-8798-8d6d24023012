import { GameSave } from '@/types/game'
import { databaseService } from './database'
import { createNewGameSave, validateGameSave } from '@/utils/gameData'
import { STORAGE_KEYS } from '@/constants/game'

export interface SaveInfo {
  id: string
  name: string
  createdAt: number
  lastSaved: number
  day: number
  level: number
  playtime: number
}

// 游戏存档管理服务
export class SaveManagerService {
  private currentSave: GameSave | null = null
  private autoSaveInterval: number | null = null
  private readonly autoSaveIntervalMs = 5 * 60 * 1000 // 5分钟自动保存

  // 初始化存档管理器
  async init(): Promise<void> {
    await databaseService.init()
  }

  // 创建新游戏
  async createNewGame(): Promise<GameSave> {
    const newSave = createNewGameSave()
    this.currentSave = newSave
    
    // 保存到数据库
    await this.saveCurrentGame()
    
    // 启动自动保存
    this.startAutoSave()
    
    return newSave
  }

  // 加载游戏
  async loadGame(saveId: string): Promise<GameSave> {
    const save = await databaseService.loadGame(saveId)
    
    if (!save) {
      throw new Error('Save not found')
    }
    
    if (!validateGameSave(save)) {
      throw new Error('Invalid save data')
    }
    
    this.currentSave = save
    
    // 启动自动保存
    this.startAutoSave()
    
    return save
  }

  // 保存当前游戏
  async saveCurrentGame(): Promise<void> {
    if (!this.currentSave) {
      throw new Error('No current game to save')
    }
    
    await databaseService.saveGame(this.currentSave)
    
    // 同时保存到localStorage作为备份
    try {
      localStorage.setItem(STORAGE_KEYS.GAME_SAVE, JSON.stringify(this.currentSave))
    } catch (error) {
      console.warn('Failed to save to localStorage:', error)
    }
  }

  // 获取当前游戏存档
  getCurrentSave(): GameSave | null {
    return this.currentSave
  }

  // 更新当前游戏存档
  updateCurrentSave(updates: Partial<GameSave>): void {
    if (!this.currentSave) {
      throw new Error('No current game to update')
    }
    
    this.currentSave = {
      ...this.currentSave,
      ...updates,
      lastSaved: Date.now()
    }
  }

  // 获取所有存档信息
  async getAllSaveInfo(): Promise<SaveInfo[]> {
    const saves = await databaseService.getAllSaves()
    
    return saves.map(save => ({
      id: save.id,
      name: `存档 - 第${save.gameTime.day}天`,
      createdAt: save.createdAt,
      lastSaved: save.lastSaved,
      day: save.gameTime.day,
      level: save.playerStatus.level,
      playtime: save.stats.totalPlayTime
    }))
  }

  // 删除存档
  async deleteSave(saveId: string): Promise<void> {
    await databaseService.deleteSave(saveId)
    
    // 如果删除的是当前存档，清空当前存档
    if (this.currentSave && this.currentSave.id === saveId) {
      this.currentSave = null
      this.stopAutoSave()
    }
  }

  // 检查是否有存档
  async hasSaves(): Promise<boolean> {
    return await databaseService.hasSaves()
  }

  // 获取最新存档
  async getLatestSave(): Promise<GameSave | null> {
    return await databaseService.getLatestSave()
  }

  // 导出存档
  exportSave(saveId?: string): string {
    const save = saveId ? null : this.currentSave
    if (!save) {
      throw new Error('No save to export')
    }
    
    return JSON.stringify(save, null, 2)
  }

  // 导入存档
  async importSave(saveData: string): Promise<GameSave> {
    let save: any
    
    try {
      save = JSON.parse(saveData)
    } catch (error) {
      throw new Error('Invalid save data format')
    }
    
    if (!validateGameSave(save)) {
      throw new Error('Invalid save data')
    }
    
    // 生成新的ID避免冲突
    save.id = `imported_${Date.now()}`
    save.lastSaved = Date.now()
    
    await databaseService.saveGame(save)
    
    return save
  }

  // 启动自动保存
  private startAutoSave(): void {
    this.stopAutoSave()
    
    this.autoSaveInterval = window.setInterval(async () => {
      if (this.currentSave && this.currentSave.settings.autoSave) {
        try {
          await this.saveCurrentGame()
          console.log('Auto-saved game')
        } catch (error) {
          console.error('Auto-save failed:', error)
        }
      }
    }, this.autoSaveIntervalMs)
  }

  // 停止自动保存
  private stopAutoSave(): void {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval)
      this.autoSaveInterval = null
    }
  }

  // 从localStorage恢复存档
  async recoverFromLocalStorage(): Promise<GameSave | null> {
    try {
      const saveData = localStorage.getItem(STORAGE_KEYS.GAME_SAVE)
      if (!saveData) return null
      
      const save = JSON.parse(saveData)
      if (!validateGameSave(save)) return null
      
      // 保存到IndexedDB
      await databaseService.saveGame(save)
      
      return save
    } catch (error) {
      console.error('Failed to recover from localStorage:', error)
      return null
    }
  }

  // 清空所有存档
  async clearAllSaves(): Promise<void> {
    await databaseService.clearAllData()
    
    // 清空localStorage
    try {
      localStorage.removeItem(STORAGE_KEYS.GAME_SAVE)
    } catch (error) {
      console.warn('Failed to clear localStorage:', error)
    }
    
    this.currentSave = null
    this.stopAutoSave()
  }

  // 获取存储使用情况
  async getStorageUsage(): Promise<{ used: number; quota?: number }> {
    const used = await databaseService.getDatabaseSize()
    
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate()
      return {
        used,
        quota: estimate.quota
      }
    }
    
    return { used }
  }

  // 格式化存储大小
  formatStorageSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`
  }

  // 销毁服务
  destroy(): void {
    this.stopAutoSave()
    this.currentSave = null
    databaseService.close()
  }
}

// 创建单例实例
export const saveManager = new SaveManagerService()
