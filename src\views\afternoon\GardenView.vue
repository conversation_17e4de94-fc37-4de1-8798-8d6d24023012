<template>
  <div class="garden-view">
    <a-card title="孤独园" class="garden-card">
      <template #extra>
        <a-space>
          <a-tag color="green">体力: {{ playerStatus.energy }}/{{ playerStatus.maxEnergy }}</a-tag>
          <a-tag color="gold">灵石: {{ playerStatus.money }}</a-tag>
          <a-button @click="goBack">返回地图</a-button>
        </a-space>
      </template>

      <div class="garden-content">
        <a-row gutter={24}>
          <!-- 普通招募 -->
          <a-col span={12}>
            <a-card hoverable class="recruit-card normal-recruit">
              <template #cover>
                <div class="recruit-cover normal-cover">
                  <UserOutlined />
                </div>
              </template>
              <a-card-meta title="普通招募" description="招募普通少女，消耗灵石" />
              <div class="recruit-info">
                <div class="cost">消耗: 100 灵石</div>
                <div class="success-rate">成功率: 100%</div>
                <div class="description">可获得普通稀有度的少女</div>
              </div>
              <div class="recruit-actions">
                <a-button type="primary" size="large" @click="normalRecruit" :disabled="!canNormalRecruit"
                  :loading="recruiting">
                  招募 (100 灵石)
                </a-button>
              </div>
            </a-card>
          </a-col>

          <!-- 稀有招募 -->
          <a-col span={12}>
            <a-card hoverable class="recruit-card rare-recruit" :class="{ disabled: !rareRecruitAvailable }">
              <template #cover>
                <div class="recruit-cover rare-cover">
                  <CrownOutlined />
                </div>
              </template>
              <a-card-meta title="稀有招募" description="招募稀有少女，需要特殊材料" />
              <div class="recruit-info">
                <div class="cost">消耗: 500 灵石 + 1 琼浆</div>
                <div class="success-rate">成功率: 80%</div>
                <div class="description">可获得稀有或特殊稀有度的少女</div>
                <div v-if="!rareRecruitAvailable" class="unavailable">
                  <a-tag color="orange">小概率出现</a-tag>
                </div>
              </div>
              <div class="recruit-actions">
                <a-button type="primary" size="large" @click="rareRecruit" :disabled="!canRareRecruit"
                  :loading="recruiting">
                  招募 (500 灵石 + 1 琼浆)
                </a-button>
              </div>
            </a-card>
          </a-col>
        </a-row>

        <!-- 招募历史 -->
        <a-card title="最近招募" class="history-card" style="margin-top: 24px;">
          <a-list :data-source="recruitHistory" :locale="{ emptyText: '暂无招募记录' }">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <span>{{ item.characterName }}</span>
                    <a-tag :color="getRarityColor(item.rarity)" style="margin-left: 8px;">
                      {{ getRarityText(item.rarity) }}
                    </a-tag>
                  </template>
                  <template #description>
                    {{ item.description }} - {{ item.time }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { UserOutlined, CrownOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { gameStore } from '@/stores/gameStore'
import { Character, Rarity } from '@/types/game'
import { generateId } from '@/utils/gameData'

interface RecruitRecord {
  id: string
  characterName: string
  rarity: 'normal' | 'rare' | 'special'
  description: string
  time: string
}

const router = useRouter()

// 从状态管理获取玩家状态
const { playerStatus, spendMoney, consumeEnergy, addCharacter, getItemQuantity } = gameStore

// 招募状态
const recruiting = ref(false)
const rareRecruitAvailable = ref(false)

// 招募历史
const recruitHistory = ref<RecruitRecord[]>([])

// 普通招募角色池
const normalCharacters = [
  { name: '小雅', description: '温柔善良的少女' },
  { name: '小梅', description: '活泼可爱的少女' },
  { name: '小兰', description: '文静优雅的少女' },
  { name: '小菊', description: '勤劳朴实的少女' }
]

// 稀有招募角色池
const rareCharacters = [
  { name: '紫萱', description: '聪慧过人的才女', rarity: 'rare' as const },
  { name: '青莲', description: '冰清玉洁的仙子', rarity: 'rare' as const },
  { name: '凤凰', description: '神秘强大的存在', rarity: 'special' as const }
]

// 检查是否可以普通招募
const canNormalRecruit = computed(() => {
  return playerStatus.money >= 100 && playerStatus.energy > 0 && !recruiting.value
})

// 检查是否可以稀有招募
const canRareRecruit = computed(() => {
  return rareRecruitAvailable.value &&
    playerStatus.money >= 500 &&
    getItemQuantity('nectar') >= 1 &&
    playerStatus.energy > 0 &&
    !recruiting.value
})

// 获取稀有度颜色
const getRarityColor = (rarity: string) => {
  const colors = {
    normal: 'default',
    rare: 'blue',
    special: 'purple'
  }
  return colors[rarity as keyof typeof colors] || 'default'
}

// 获取稀有度文本
const getRarityText = (rarity: string) => {
  const texts = {
    normal: '普通',
    rare: '稀有',
    special: '特殊'
  }
  return texts[rarity as keyof typeof texts] || '未知'
}

// 普通招募
const normalRecruit = async () => {
  if (!canNormalRecruit.value) {
    message.error('无法进行普通招募')
    return
  }

  recruiting.value = true

  try {
    // 模拟招募过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 随机选择角色
    const characterTemplate = normalCharacters[Math.floor(Math.random() * normalCharacters.length)]

    // 创建新角色
    const newCharacter: Character = {
      id: generateId('char_'),
      name: characterTemplate.name,
      description: characterTemplate.description,
      rarity: Rarity.NORMAL,
      level: 1,
      exp: 0,
      maxExp: 100,
      attributes: {
        charm: 10 + Math.floor(Math.random() * 10),
        skill: 10 + Math.floor(Math.random() * 10),
        stamina: 10 + Math.floor(Math.random() * 10),
        wisdom: 10 + Math.floor(Math.random() * 10)
      },
      isWorking: false,
      storylineCompleted: false
    }

    // 扣除资源
    spendMoney(100)
    consumeEnergy(1)

    // 添加角色到游戏
    addCharacter(newCharacter)

    // 添加招募记录
    const record: RecruitRecord = {
      id: Date.now().toString(),
      characterName: newCharacter.name,
      rarity: 'normal',
      description: newCharacter.description,
      time: new Date().toLocaleTimeString()
    }
    recruitHistory.value.unshift(record)

    message.success(`成功招募到 ${newCharacter.name}！`)

    // 体力耗尽自动返回
    if (playerStatus.energy <= 0) {
      message.warning('体力耗尽，自动返回地图')
      setTimeout(() => goBack(), 1000)
    }

  } catch (error) {
    message.error('招募失败')
  } finally {
    recruiting.value = false
  }
}

// 稀有招募
const rareRecruit = async () => {
  if (!canRareRecruit.value) {
    message.error('无法进行稀有招募')
    return
  }

  recruiting.value = true

  try {
    // 模拟招募过程
    await new Promise(resolve => setTimeout(resolve, 3000))

    // 80% 成功率
    if (Math.random() > 0.8) {
      message.error('招募失败，但仍然消耗了材料')
      spendMoney(500)
      gameStore.removeItem('nectar', 1)
      consumeEnergy(1)
      return
    }

    // 随机选择角色
    const characterTemplate = rareCharacters[Math.floor(Math.random() * rareCharacters.length)]

    // 创建新角色
    const newCharacter: Character = {
      id: generateId('char_'),
      name: characterTemplate.name,
      description: characterTemplate.description,
      rarity: characterTemplate.rarity as Rarity,
      level: 1,
      exp: 0,
      maxExp: 100,
      attributes: {
        charm: 15 + Math.floor(Math.random() * 15),
        skill: 15 + Math.floor(Math.random() * 15),
        stamina: 15 + Math.floor(Math.random() * 15),
        wisdom: 15 + Math.floor(Math.random() * 15)
      },
      isWorking: false,
      storylineCompleted: false
    }

    // 扣除资源
    spendMoney(500)
    gameStore.removeItem('nectar', 1)
    consumeEnergy(1)

    // 添加角色到游戏
    addCharacter(newCharacter)

    // 添加招募记录
    const record: RecruitRecord = {
      id: Date.now().toString(),
      characterName: newCharacter.name,
      rarity: characterTemplate.rarity,
      description: newCharacter.description,
      time: new Date().toLocaleTimeString()
    }
    recruitHistory.value.unshift(record)

    message.success(`成功招募到 ${newCharacter.name}！`)

    // 稀有招募后消失
    rareRecruitAvailable.value = false

    // 体力耗尽自动返回
    if (playerStatus.energy <= 0) {
      message.warning('体力耗尽，自动返回地图')
      setTimeout(() => goBack(), 1000)
    }

  } catch (error) {
    message.error('招募失败')
  } finally {
    recruiting.value = false
  }
}

const goBack = () => {
  router.push({ name: 'map' })
}

onMounted(() => {
  // 随机决定稀有招募是否可用（小概率）
  rareRecruitAvailable.value = Math.random() < 0.3
})
</script>

<style scoped>
.garden-view {
  height: 100%;
  padding: 16px;
}

.garden-card {
  height: 100%;
}

.garden-content {
  padding: 16px 0;
}

.recruit-card {
  transition: all 0.3s ease;
}

.recruit-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.recruit-card.disabled {
  opacity: 0.6;
}

.recruit-cover {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: white;
}

.normal-cover {
  background: linear-gradient(135deg, #a8a8a8 0%, #d3d3d3 100%);
}

.rare-cover {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.recruit-info {
  margin: 16px 0;
}

.cost,
.success-rate,
.description {
  margin-bottom: 8px;
  font-size: 14px;
}

.cost {
  color: #f5222d;
  font-weight: bold;
}

.success-rate {
  color: #52c41a;
}

.description {
  color: #666;
}

.unavailable {
  margin-top: 8px;
}

.recruit-actions {
  text-align: center;
  margin-top: 16px;
}

.history-card {
  max-height: 300px;
  overflow-y: auto;
}
</style>
