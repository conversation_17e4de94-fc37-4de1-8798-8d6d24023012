<template>
  <div class="black-market-auction">
    <a-card title="黑市拍卖会" class="auction-card">
      <template #extra>
        <a-tag v-if="!isOpen" color="red">未开放</a-tag>
        <a-tag v-else color="purple">拍卖进行中</a-tag>
      </template>

      <div v-if="!isOpen" class="closed-notice">
        <a-empty description="黑市拍卖会仅在周六下午开放">
          <template #image>
            <ClockCircleOutlined style="font-size: 48px; color: #ccc;" />
          </template>
        </a-empty>
      </div>

      <div v-else class="auction-content">
        <!-- 拍卖规则说明 -->
        <a-alert message="拍卖规则" description="每次拍卖提供3-5件稀有物品，出价最高者获得。拍卖结束后立即结算。" type="info" show-icon
          style="margin-bottom: 16px;" />

        <!-- 当前拍卖物品 -->
        <div class="auction-items">
          <a-row :gutter="16">
            <a-col v-for="auction in currentAuctions" :key="auction.id" :xs="24" :sm="12" :md="8">
              <a-card :title="auction.item.name" size="small" class="auction-item-card"
                :class="`rarity-${auction.item.rarity}`">
                <template #cover>
                  <div class="item-preview">
                    <img :src="getItemImage(auction.item.iconId)" :alt="auction.item.name" @error="handleImageError" />
                    <div class="rarity-badge">
                      <a-tag :color="getRarityColor(auction.item.rarity)">
                        {{ getRarityText(auction.item.rarity) }}
                      </a-tag>
                    </div>
                  </div>
                </template>

                <div class="auction-info">
                  <p class="item-description">{{ auction.item.description }}</p>

                  <div class="bid-info">
                    <div class="current-bid">
                      <span class="label">当前出价:</span>
                      <span class="amount">{{ auction.currentBid }}灵石</span>
                    </div>
                    <div class="bidder" v-if="auction.currentBidder">
                      <span class="label">出价者:</span>
                      <span class="name">{{ auction.currentBidder }}</span>
                    </div>
                    <div class="time-left">
                      <span class="label">剩余时间:</span>
                      <span class="time">{{ formatTimeLeft(auction.endTime) }}</span>
                    </div>
                  </div>

                  <div class="bid-actions">
                    <a-input-number v-model:value="bidAmounts[auction.id]"
                      :min="auction.currentBid + auction.minIncrement" :max="playerMoney" :step="auction.minIncrement"
                      placeholder="出价金额" style="width: 100%; margin-bottom: 8px;" />
                    <a-button type="primary" :disabled="!canBid(auction)" @click="placeBid(auction)" block>
                      出价
                    </a-button>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 拍卖历史 -->
        <div class="auction-history">
          <a-divider>今日拍卖记录</a-divider>
          <a-table :columns="historyColumns" :data-source="auctionHistory" :pagination="false" size="small">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'item'">
                <span :class="`rarity-${record.item.rarity}`">
                  {{ record.item.name }}
                </span>
              </template>
              <template v-if="column.key === 'result'">
                <a-tag :color="record.winner === '玩家' ? 'green' : 'default'">
                  {{ record.winner }}
                </a-tag>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ClockCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { gameStore } from '@/stores/gameStore'
import { timeManager } from '@/utils/timeManager'

// 拍卖物品接口
interface AuctionItem {
  id: string
  name: string
  description: string
  rarity: string
  iconId: string
  baseValue: number
}

// 拍卖信息接口
interface AuctionInfo {
  id: string
  item: AuctionItem
  startBid: number
  currentBid: number
  currentBidder: string | null
  minIncrement: number
  endTime: number
}

// 拍卖历史记录
interface AuctionRecord {
  id: string
  item: AuctionItem
  finalBid: number
  winner: string
  timestamp: number
}

// 响应式数据
const currentAuctions = ref<AuctionInfo[]>([])
const auctionHistory = ref<AuctionRecord[]>([])
const bidAmounts = ref<Record<string, number>>({})
const updateTimer = ref<number | null>(null)

// 计算属性
const isOpen = computed(() => {
  const time = timeManager.getCurrentTime()
  return time.dayOfWeek === 6 && timeManager.canAccessLocation('black_market')
})

const playerMoney = computed(() => gameStore.playerStatus.value.money)

// 表格列配置
const historyColumns = [
  { title: '物品', dataIndex: 'item', key: 'item' },
  { title: '成交价', dataIndex: 'finalBid', key: 'finalBid' },
  { title: '获得者', dataIndex: 'winner', key: 'result' },
]

// 生成拍卖物品
const generateAuctionItems = (): AuctionItem[] => {
  const items: AuctionItem[] = [
    {
      id: 'rare_character_scroll',
      name: '稀有角色召唤卷轴',
      description: '可以召唤一名稀有角色',
      rarity: 'epic',
      iconId: 'scroll',
      baseValue: 2000
    },
    {
      id: 'legendary_nectar',
      name: '传说琼浆',
      description: '极其珍贵的琼浆，效果卓越',
      rarity: 'legendary',
      iconId: 'nectar_legendary',
      baseValue: 5000
    },
    {
      id: 'workshop_upgrade_token',
      name: '工坊升级令牌',
      description: '可以增加任意工坊的槽位数量',
      rarity: 'epic',
      iconId: 'token',
      baseValue: 3000
    },
    {
      id: 'skill_point_crystal',
      name: '技能点水晶',
      description: '使用后获得额外技能点',
      rarity: 'rare',
      iconId: 'crystal',
      baseValue: 1500
    },
    {
      id: 'time_acceleration_hourglass',
      name: '时间加速沙漏',
      description: '可以立即完成一个派遣任务',
      rarity: 'rare',
      iconId: 'hourglass',
      baseValue: 1000
    }
  ]

  // 随机选择3-5个物品
  const count = Math.floor(Math.random() * 3) + 3
  const shuffled = [...items].sort(() => Math.random() - 0.5)
  return shuffled.slice(0, count)
}

// 初始化拍卖
const initializeAuctions = () => {
  if (!isOpen.value) return

  const items = generateAuctionItems()
  const now = Date.now()
  const auctionDuration = 5 * 60 * 1000 // 5分钟

  currentAuctions.value = items.map((item, index) => ({
    id: `auction_${now}_${index}`,
    item,
    startBid: Math.floor(item.baseValue * 0.5),
    currentBid: Math.floor(item.baseValue * 0.5),
    currentBidder: null,
    minIncrement: Math.floor(item.baseValue * 0.1),
    endTime: now + auctionDuration + (index * 30000) // 每个拍卖错开30秒
  }))

  // 初始化出价金额
  bidAmounts.value = {}
  currentAuctions.value.forEach(auction => {
    bidAmounts.value[auction.id] = auction.currentBid + auction.minIncrement
  })
}

// 检查是否可以出价
const canBid = (auction: AuctionInfo): boolean => {
  const bidAmount = bidAmounts.value[auction.id]
  return bidAmount >= auction.currentBid + auction.minIncrement &&
    bidAmount <= playerMoney.value &&
    Date.now() < auction.endTime
}

// 出价
const placeBid = (auction: AuctionInfo) => {
  const bidAmount = bidAmounts.value[auction.id]

  if (!canBid(auction)) {
    message.error('出价无效')
    return
  }

  // 模拟其他竞拍者
  const hasCompetition = Math.random() < 0.3 // 30%概率有竞争

  if (hasCompetition) {
    const competitorBid = bidAmount + auction.minIncrement + Math.floor(Math.random() * auction.minIncrement * 2)
    if (competitorBid <= auction.item.baseValue * 1.5) {
      auction.currentBid = competitorBid
      auction.currentBidder = '神秘买家'
      bidAmounts.value[auction.id] = competitorBid + auction.minIncrement
      message.warning(`被其他买家超越！当前出价: ${competitorBid}灵石`)
      return
    }
  }

  auction.currentBid = bidAmount
  auction.currentBidder = '玩家'
  bidAmounts.value[auction.id] = bidAmount + auction.minIncrement

  message.success(`出价成功！当前最高价: ${bidAmount}灵石`)
}

// 处理拍卖结束
const handleAuctionEnd = (auction: AuctionInfo) => {
  if (auction.currentBidder === '玩家') {
    // 玩家获胜
    if (gameStore.spendMoney(auction.currentBid)) {
      // 根据物品类型给予奖励
      handleAuctionReward(auction.item)
      message.success(`恭喜！您以 ${auction.currentBid}灵石 获得了 ${auction.item.name}`)
    } else {
      message.error('余额不足，无法完成交易')
      auction.currentBidder = null
    }
  }

  // 添加到历史记录
  auctionHistory.value.push({
    id: auction.id,
    item: auction.item,
    finalBid: auction.currentBid,
    winner: auction.currentBidder || '流拍',
    timestamp: Date.now()
  })
}

// 处理拍卖奖励
const handleAuctionReward = (item: AuctionItem) => {
  switch (item.id) {
    case 'rare_character_scroll':
      // 生成稀有角色的逻辑
      message.info('稀有角色召唤卷轴已添加到背包')
      gameStore.addItem('rare_character_scroll', 1)
      break
    case 'legendary_nectar':
      gameStore.addItem('nectar', 5)
      break
    case 'workshop_upgrade_token':
      gameStore.addItem('workshop_upgrade_token', 1)
      break
    case 'skill_point_crystal':
      // 增加技能点的逻辑
      message.info('获得了额外的技能点')
      break
    case 'time_acceleration_hourglass':
      gameStore.addItem('time_acceleration_hourglass', 1)
      break
    default:
      gameStore.addItem(item.id, 1)
  }
}

// 格式化剩余时间
const formatTimeLeft = (endTime: number): string => {
  const remaining = Math.max(0, endTime - Date.now())
  const minutes = Math.floor(remaining / 60000)
  const seconds = Math.floor((remaining % 60000) / 1000)

  if (remaining === 0) return '已结束'
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

// 获取物品图片
const getItemImage = (iconId: string): string => {
  return `/src/assets/graphics/icons/items/${iconId}.png`
}

// 处理图片错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/src/assets/graphics/icons/default.png'
}

// 获取稀有度颜色
const getRarityColor = (rarity: string): string => {
  const colors = {
    common: 'default',
    rare: 'blue',
    epic: 'purple',
    legendary: 'gold'
  }
  return colors[rarity as keyof typeof colors] || 'default'
}

// 获取稀有度文本
const getRarityText = (rarity: string): string => {
  const texts = {
    common: '普通',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说'
  }
  return texts[rarity as keyof typeof texts] || '未知'
}

// 更新拍卖状态
const updateAuctions = () => {
  const now = Date.now()

  currentAuctions.value.forEach(auction => {
    if (now >= auction.endTime && auction.currentBidder) {
      handleAuctionEnd(auction)
    }
  })

  // 移除已结束的拍卖
  currentAuctions.value = currentAuctions.value.filter(auction => now < auction.endTime)
}

// 生命周期
onMounted(() => {
  if (isOpen.value) {
    initializeAuctions()

    // 每秒更新一次
    updateTimer.value = window.setInterval(updateAuctions, 1000)
  }
})

onUnmounted(() => {
  if (updateTimer.value) {
    clearInterval(updateTimer.value)
  }
})

// 监听时间变化
timeManager.addTimeListener(() => {
  if (isOpen.value && currentAuctions.value.length === 0) {
    initializeAuctions()
  }
})
</script>

<style scoped>
.black-market-auction {
  padding: 16px;
}

.auction-card {
  max-width: 1200px;
  margin: 0 auto;
}

.closed-notice {
  text-align: center;
  padding: 64px 16px;
}

.auction-content {
  min-height: 400px;
}

.auction-items {
  margin-bottom: 32px;
}

.auction-item-card {
  height: 100%;
  transition: all 0.3s ease;
}

.auction-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.auction-item-card.rarity-rare {
  border: 2px solid #1890ff;
}

.auction-item-card.rarity-epic {
  border: 2px solid #722ed1;
}

.auction-item-card.rarity-legendary {
  border: 2px solid #faad14;
}

.item-preview {
  position: relative;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
}

.item-preview img {
  width: 64px;
  height: 64px;
  object-fit: contain;
}

.rarity-badge {
  position: absolute;
  top: 8px;
  right: 8px;
}

.auction-info {
  padding: 8px 0;
}

.item-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
  line-height: 1.4;
}

.bid-info {
  margin-bottom: 12px;
}

.bid-info>div {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.label {
  color: #666;
}

.amount {
  font-weight: bold;
  color: #faad14;
}

.name {
  font-weight: bold;
  color: #1890ff;
}

.time {
  font-weight: bold;
  color: #f5222d;
}

.auction-history {
  margin-top: 32px;
}

.rarity-rare {
  color: #1890ff;
}

.rarity-epic {
  color: #722ed1;
}

.rarity-legendary {
  color: #faad14;
}
</style>
