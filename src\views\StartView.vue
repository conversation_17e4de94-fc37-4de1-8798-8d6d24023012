<template>
  <div class="start-view">
    <div class="start-container">
      <div class="game-logo">
        <h1>琉璃胭脂坊</h1>
        <p>经营养成游戏</p>
      </div>

      <div class="start-buttons">
        <a-space direction="vertical" size="large">
          <a-button type="primary" size="large" @click="startNewGame" :loading="loading">
            新游戏
          </a-button>
          <a-button size="large" @click="loadGame" :disabled="!hasSaveData">
            读取存档
          </a-button>
          <a-button size="large" @click="showSettings">
            设置
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 设置模态框 -->
    <a-modal v-model:open="settingsVisible" title="游戏设置" @ok="saveSettings">
      <a-form layout="vertical">
        <a-form-item label="音效音量">
          <a-slider v-model:value="settings.soundVolume" :min="0" :max="100" />
        </a-form-item>
        <a-form-item label="背景音乐音量">
          <a-slider v-model:value="settings.musicVolume" :min="0" :max="100" />
        </a-form-item>
        <a-form-item label="自动保存">
          <a-switch v-model:checked="settings.autoSave" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { gameStore } from '@/stores/gameStore'
import { saveManager } from '@/services/saveManager'

const router = useRouter()

const loading = ref(false)
const hasSaveData = ref(false)
const settingsVisible = ref(false)

const settings = ref({
  soundVolume: 80,
  musicVolume: 60,
  autoSave: true
})

// 检查是否有存档数据
onMounted(async () => {
  try {
    await gameStore.initializeGame()
    hasSaveData.value = await saveManager.hasSaves()
  } catch (error) {
    console.error('Failed to initialize:', error)
  }
})

// 开始新游戏
const startNewGame = async () => {
  loading.value = true
  try {
    await gameStore.startNewGame()
    message.success('游戏开始！')
    router.push({ name: 'map' })
  } catch (error) {
    message.error('游戏启动失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 读取存档
const loadGame = async () => {
  try {
    const latestSave = await saveManager.getLatestSave()
    if (latestSave) {
      await gameStore.loadGame(latestSave.id)
      message.success('存档读取成功！')
      router.push({ name: 'map' })
    } else {
      message.error('没有找到存档')
    }
  } catch (error) {
    message.error('存档读取失败')
    console.error(error)
  }
}

// 显示设置
const showSettings = () => {
  settingsVisible.value = true
}

// 保存设置
const saveSettings = () => {
  // TODO: 保存设置到本地存储
  message.success('设置已保存')
  settingsVisible.value = false
}
</script>

<style scoped>
.start-view {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.start-view::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.start-container {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 60px 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.game-logo h1 {
  font-size: 48px;
  margin: 0 0 10px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
}

.game-logo p {
  font-size: 18px;
  color: #666;
  margin: 0 0 40px 0;
}

.start-buttons {
  margin-top: 40px;
}

.start-buttons .ant-btn {
  width: 200px;
  height: 50px;
  font-size: 16px;
  border-radius: 25px;
}
</style>
