<template>
  <div class="bedroom-view">
    <a-card title="寝室" class="bedroom-card">
      <template #extra>
        <a-space>
          <a-tag color="purple">晚间时段</a-tag>
          <a-button @click="goBack">返回地图</a-button>
        </a-space>
      </template>

      <div class="bedroom-content">
        <a-row gutter={24}>
          <!-- 用餐 -->
          <a-col span={8}>
            <a-card 
              hoverable 
              class="activity-card"
              :class="{ disabled: dailyActivities.meal }"
              @click="meal"
            >
              <template #cover>
                <div class="activity-icon meal-icon">
                  <GiftOutlined />
                </div>
              </template>
              <a-card-meta title="用餐" description="享用美味晚餐，恢复体力" />
              <div class="activity-info">
                <div class="effect">效果: 恢复 3 点体力</div>
                <div class="status">
                  <a-tag v-if="dailyActivities.meal" color="green">已完成</a-tag>
                  <a-tag v-else color="blue">可进行</a-tag>
                </div>
              </div>
            </a-card>
          </a-col>

          <!-- 洗澡 -->
          <a-col span={8}>
            <a-card 
              hoverable 
              class="activity-card"
              :class="{ disabled: dailyActivities.bath }"
              @click="bath"
            >
              <template #cover>
                <div class="activity-icon bath-icon">
                  <ExperimentOutlined />
                </div>
              </template>
              <a-card-meta title="洗澡" description="舒缓身心，提升心情" />
              <div class="activity-info">
                <div class="effect">效果: 恢复 2 点体力，提升心情</div>
                <div class="status">
                  <a-tag v-if="dailyActivities.bath" color="green">已完成</a-tag>
                  <a-tag v-else color="blue">可进行</a-tag>
                </div>
              </div>
            </a-card>
          </a-col>

          <!-- 睡觉 -->
          <a-col span={8}>
            <a-card hoverable class="activity-card" @click="sleep">
              <template #cover>
                <div class="activity-icon sleep-icon">
                  <HomeOutlined />
                </div>
              </template>
              <a-card-meta title="睡觉" description="结束一天，进入下一天" />
              <div class="activity-info">
                <div class="effect">效果: 完全恢复体力，进入新的一天</div>
                <div class="status">
                  <a-tag color="orange">结束当天</a-tag>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>

        <!-- 特殊活动 -->
        <a-card title="特殊活动" class="special-activities" style="margin-top: 24px;">
          <a-row gutter={16}>
            <!-- 双修 -->
            <a-col span={12}>
              <a-card 
                hoverable 
                class="special-card"
                :class="{ disabled: !canDoubleRest }"
                @click="doubleRest"
              >
                <template #cover>
                  <div class="special-icon double-icon">
                    <HeartOutlined />
                  </div>
                </template>
                <a-card-meta title="双修" description="与角色进行特殊互动" />
                <div class="special-info">
                  <div class="requirement">需求: 完成单个角色支线</div>
                  <div class="cost">消耗: 2-3 点体力</div>
                  <div class="status">
                    <a-tag v-if="canDoubleRest" color="purple">可进行</a-tag>
                    <a-tag v-else color="default">未解锁</a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>

            <!-- 群修 -->
            <a-col span={12}>
              <a-card 
                hoverable 
                class="special-card"
                :class="{ disabled: !canGroupRest }"
                @click="groupRest"
              >
                <template #cover>
                  <div class="special-icon group-icon">
                    <CrownOutlined />
                  </div>
                </template>
                <a-card-meta title="群修" description="与多个角色进行特殊互动" />
                <div class="special-info">
                  <div class="requirement">需求: 完成所有角色支线</div>
                  <div class="cost">消耗: 4-5 点体力</div>
                  <div class="status">
                    <a-tag v-if="canGroupRest" color="purple">可进行</a-tag>
                    <a-tag v-else color="default">未解锁</a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </a-card>

        <!-- 当日总结 -->
        <a-card title="今日总结" class="daily-summary" style="margin-top: 24px;">
          <a-row gutter={16}>
            <a-col span={6}>
              <a-statistic title="获得经验" :value="dailySummary.exp" suffix="点" />
            </a-col>
            <a-col span={6}>
              <a-statistic title="获得灵石" :value="dailySummary.money" suffix="个" />
            </a-col>
            <a-col span={6}>
              <a-statistic title="完成任务" :value="dailySummary.tasks" suffix="个" />
            </a-col>
            <a-col span={6}>
              <a-statistic title="消耗体力" :value="dailySummary.energy" suffix="点" />
            </a-col>
          </a-row>
        </a-card>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { 
  GiftOutlined,
  ExperimentOutlined,
  HomeOutlined,
  HeartOutlined,
  CrownOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'

const router = useRouter()

// 玩家状态
const playerEnergy = ref(5)
const playerMaxEnergy = ref(10)

// 每日活动状态
const dailyActivities = ref({
  meal: false,
  bath: false
})

// 解锁状态
const unlockedFeatures = ref({
  doubleRest: false, // 需要完成单个角色支线
  groupRest: false   // 需要完成所有角色支线
})

// 今日总结
const dailySummary = ref({
  exp: 150,
  money: 300,
  tasks: 2,
  energy: 8
})

// 是否可以进行双修
const canDoubleRest = computed(() => unlockedFeatures.value.doubleRest)

// 是否可以进行群修
const canGroupRest = computed(() => unlockedFeatures.value.groupRest)

// 用餐
const meal = () => {
  if (dailyActivities.value.meal) {
    message.warning('今天已经用过餐了')
    return
  }

  dailyActivities.value.meal = true
  playerEnergy.value = Math.min(playerMaxEnergy.value, playerEnergy.value + 3)
  message.success('享用了美味的晚餐，恢复了 3 点体力')
}

// 洗澡
const bath = () => {
  if (dailyActivities.value.bath) {
    message.warning('今天已经洗过澡了')
    return
  }

  dailyActivities.value.bath = true
  playerEnergy.value = Math.min(playerMaxEnergy.value, playerEnergy.value + 2)
  message.success('舒缓的热水澡让你神清气爽，恢复了 2 点体力')
}

// 睡觉
const sleep = () => {
  Modal.confirm({
    title: '确认睡觉',
    content: '睡觉将结束今天，进入新的一天。确定要睡觉吗？',
    onOk() {
      // 完全恢复体力
      playerEnergy.value = playerMaxEnergy.value
      
      // 重置每日活动
      dailyActivities.value = {
        meal: false,
        bath: false
      }
      
      message.success('美好的一天结束了，明天又是新的开始！')
      
      // 返回地图并推进到新的一天
      setTimeout(() => {
        router.push({ name: 'map' })
      }, 1500)
    }
  })
}

// 双修
const doubleRest = () => {
  if (!canDoubleRest.value) {
    message.warning('需要先完成单个角色的支线任务才能解锁此功能')
    return
  }

  if (playerEnergy.value < 2) {
    message.error('体力不足，需要至少 2 点体力')
    return
  }

  message.info('双修功能开发中...')
}

// 群修
const groupRest = () => {
  if (!canGroupRest.value) {
    message.warning('需要完成所有角色的支线任务才能解锁此功能')
    return
  }

  if (playerEnergy.value < 4) {
    message.error('体力不足，需要至少 4 点体力')
    return
  }

  message.info('群修功能开发中...')
}

const goBack = () => {
  router.push({ name: 'map' })
}
</script>

<style scoped>
.bedroom-view {
  height: 100%;
  padding: 16px;
}

.bedroom-card {
  height: 100%;
}

.bedroom-content {
  padding: 16px 0;
}

.activity-card, .special-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.activity-card:hover, .special-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.activity-card.disabled, .special-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.activity-card.disabled:hover, .special-card.disabled:hover {
  transform: none;
  box-shadow: none;
}

.activity-icon, .special-icon {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  color: white;
}

.meal-icon {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.bath-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.sleep-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.double-icon {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.group-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.activity-info, .special-info {
  margin-top: 12px;
}

.effect, .requirement, .cost {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.status {
  margin-top: 8px;
}

.special-activities {
  background: linear-gradient(135deg, rgba(255, 154, 158, 0.1) 0%, rgba(254, 207, 239, 0.1) 100%);
}

.daily-summary {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}
</style>
