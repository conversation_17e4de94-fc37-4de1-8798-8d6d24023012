<template>
  <a-card 
    :class="['character-card', `rarity-${character.rarity}`]"
    :hoverable="true"
    @click="$emit('click', character)"
  >
    <!-- 角色头像 -->
    <template #cover>
      <div class="character-portrait">
        <img 
          :src="getPortraitUrl(character.portraitId)" 
          :alt="character.name"
          @error="handleImageError"
        />
        <div class="rarity-badge">
          <a-tag :color="getRarityColor(character.rarity)">
            {{ getRarityText(character.rarity) }}
          </a-tag>
        </div>
        <div v-if="character.isWorking" class="working-indicator">
          <a-tag color="processing">工作中</a-tag>
        </div>
      </div>
    </template>

    <!-- 角色信息 -->
    <a-card-meta>
      <template #title>
        <div class="character-title">
          <span class="character-name">{{ character.name }}</span>
          <span class="character-level">Lv.{{ character.level }}</span>
        </div>
      </template>
      
      <template #description>
        <div class="character-description">
          {{ character.description }}
        </div>
      </template>
    </a-card-meta>

    <!-- 属性展示 -->
    <div class="character-attributes">
      <div class="attribute-row">
        <div class="attribute-item">
          <span class="attribute-label">魅力</span>
          <a-progress 
            :percent="getAttributePercent(character.attributes.charm)" 
            :show-info="false"
            stroke-color="#ff69b4"
            size="small"
          />
          <span class="attribute-value">{{ character.attributes.charm }}</span>
        </div>
        <div class="attribute-item">
          <span class="attribute-label">技艺</span>
          <a-progress 
            :percent="getAttributePercent(character.attributes.skill)" 
            :show-info="false"
            stroke-color="#52c41a"
            size="small"
          />
          <span class="attribute-value">{{ character.attributes.skill }}</span>
        </div>
      </div>
      <div class="attribute-row">
        <div class="attribute-item">
          <span class="attribute-label">体力</span>
          <a-progress 
            :percent="getAttributePercent(character.attributes.stamina)" 
            :show-info="false"
            stroke-color="#fa8c16"
            size="small"
          />
          <span class="attribute-value">{{ character.attributes.stamina }}</span>
        </div>
        <div class="attribute-item">
          <span class="attribute-label">智慧</span>
          <a-progress 
            :percent="getAttributePercent(character.attributes.wisdom)" 
            :show-info="false"
            stroke-color="#1890ff"
            size="small"
          />
          <span class="attribute-value">{{ character.attributes.wisdom }}</span>
        </div>
      </div>
    </div>

    <!-- 经验条 -->
    <div class="character-exp">
      <div class="exp-label">经验值</div>
      <a-progress 
        :percent="getExpPercent()" 
        :show-info="false"
        stroke-color="#722ed1"
        size="small"
      />
      <div class="exp-text">{{ character.exp }} / {{ character.maxExp }}</div>
    </div>

    <!-- 操作按钮 -->
    <template #actions>
      <a-tooltip title="查看详情">
        <EyeOutlined @click.stop="$emit('view', character)" />
      </a-tooltip>
      <a-tooltip title="升级角色" v-if="canLevelUp">
        <ArrowUpOutlined @click.stop="$emit('levelUp', character)" />
      </a-tooltip>
      <a-tooltip title="增加经验">
        <PlusOutlined @click.stop="$emit('addExp', character)" />
      </a-tooltip>
    </template>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { EyeOutlined, ArrowUpOutlined, PlusOutlined } from '@ant-design/icons-vue'
import type { Character } from '@/types/game'
import { Rarity } from '@/types/game'

interface Props {
  character: Character
}

interface Emits {
  (e: 'click', character: Character): void
  (e: 'view', character: Character): void
  (e: 'levelUp', character: Character): void
  (e: 'addExp', character: Character): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 获取头像URL
const getPortraitUrl = (portraitId?: string): string => {
  if (!portraitId) {
    return '/src/assets/graphics/portraits/default.png'
  }
  return `/src/assets/graphics/portraits/${portraitId}.png`
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/src/assets/graphics/portraits/default.png'
}

// 获取稀有度颜色
const getRarityColor = (rarity: Rarity): string => {
  const colors = {
    [Rarity.NORMAL]: 'default',
    [Rarity.RARE]: 'blue',
    [Rarity.EPIC]: 'purple',
    [Rarity.LEGENDARY]: 'gold'
  }
  return colors[rarity] || 'default'
}

// 获取稀有度文本
const getRarityText = (rarity: Rarity): string => {
  const texts = {
    [Rarity.NORMAL]: '普通',
    [Rarity.RARE]: '稀有',
    [Rarity.EPIC]: '史诗',
    [Rarity.LEGENDARY]: '传说'
  }
  return texts[rarity] || '未知'
}

// 获取属性百分比（基于最大值100）
const getAttributePercent = (value: number): number => {
  return Math.min((value / 100) * 100, 100)
}

// 获取经验百分比
const getExpPercent = (): number => {
  return (props.character.exp / props.character.maxExp) * 100
}

// 是否可以升级
const canLevelUp = computed(() => {
  return props.character.exp >= props.character.maxExp
})
</script>

<style scoped>
.character-card {
  width: 280px;
  margin: 8px;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
}

.character-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.character-card.rarity-normal {
  border: 2px solid #d9d9d9;
}

.character-card.rarity-rare {
  border: 2px solid #1890ff;
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
}

.character-card.rarity-epic {
  border: 2px solid #722ed1;
  box-shadow: 0 0 10px rgba(114, 46, 209, 0.3);
}

.character-card.rarity-legendary {
  border: 2px solid #faad14;
  box-shadow: 0 0 10px rgba(250, 173, 20, 0.3);
}

.character-portrait {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.character-portrait img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.character-card:hover .character-portrait img {
  transform: scale(1.05);
}

.rarity-badge {
  position: absolute;
  top: 8px;
  left: 8px;
}

.working-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
}

.character-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.character-name {
  font-weight: bold;
  font-size: 16px;
}

.character-level {
  color: #666;
  font-size: 14px;
}

.character-description {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.character-attributes {
  margin: 12px 0;
}

.attribute-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.attribute-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.attribute-label {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.attribute-value {
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  color: #333;
}

.character-exp {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.exp-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.exp-text {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .character-card {
    width: 100%;
    margin: 4px 0;
  }
  
  .character-portrait {
    height: 150px;
  }
  
  .attribute-row {
    flex-direction: column;
    gap: 4px;
  }
  
  .attribute-item {
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }
  
  .attribute-label {
    min-width: 40px;
    text-align: left;
  }
  
  .attribute-value {
    min-width: 30px;
    text-align: right;
  }
}
</style>
