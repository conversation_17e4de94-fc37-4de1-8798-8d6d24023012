<template>
  <div class="jade-guild">
    <a-card title="翡翠商会" class="guild-card">
      <template #extra>
        <a-tag color="green">营业中</a-tag>
      </template>

      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 商品购买 -->
        <a-tab-pane key="shop" tab="商品购买">
          <div class="shop-section">
            <a-alert 
              message="商会说明" 
              description="翡翠商会提供各种珍贵材料和工具，价格公道，童叟无欺。"
              type="info" 
              show-icon 
              style="margin-bottom: 16px;"
            />

            <div class="shop-filters">
              <a-space>
                <a-select v-model:value="shopFilter" style="width: 120px">
                  <a-select-option value="all">全部</a-select-option>
                  <a-select-option value="materials">材料</a-select-option>
                  <a-select-option value="tools">工具</a-select-option>
                  <a-select-option value="special">特殊</a-select-option>
                </a-select>
                <a-input 
                  v-model:value="searchText" 
                  placeholder="搜索商品"
                  style="width: 200px"
                  allow-clear
                >
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-space>
            </div>

            <div class="shop-items">
              <a-row :gutter="[16, 16]">
                <a-col 
                  v-for="item in filteredShopItems" 
                  :key="item.id"
                  :xs="24" :sm="12" :md="8" :lg="6"
                >
                  <a-card 
                    :title="item.name"
                    size="small"
                    class="shop-item-card"
                    :class="`category-${item.category}`"
                  >
                    <template #cover>
                      <div class="item-image">
                        <img 
                          :src="getItemImage(item.iconId)" 
                          :alt="item.name"
                          @error="handleImageError"
                        />
                      </div>
                    </template>

                    <div class="item-info">
                      <p class="item-description">{{ item.description }}</p>
                      <div class="item-price">
                        <span class="price">{{ item.price }}灵石</span>
                        <span v-if="item.stock" class="stock">库存: {{ item.stock }}</span>
                      </div>
                      <div class="item-actions">
                        <a-input-number
                          v-model:value="purchaseQuantities[item.id]"
                          :min="1"
                          :max="Math.min(item.stock || 99, Math.floor(playerMoney / item.price))"
                          size="small"
                          style="width: 80px; margin-right: 8px;"
                        />
                        <a-button 
                          type="primary"
                          size="small"
                          :disabled="!canPurchase(item)"
                          @click="purchaseItem(item)"
                        >
                          购买
                        </a-button>
                      </div>
                    </div>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-tab-pane>

        <!-- 物品出售 -->
        <a-tab-pane key="sell" tab="物品出售">
          <div class="sell-section">
            <a-alert 
              message="出售说明" 
              description="商会回收各种物品，价格为市场价的70%。"
              type="warning" 
              show-icon 
              style="margin-bottom: 16px;"
            />

            <div class="inventory-items">
              <a-row :gutter="[16, 16]">
                <a-col 
                  v-for="item in sellableItems" 
                  :key="item.itemId"
                  :xs="24" :sm="12" :md="8" :lg="6"
                >
                  <a-card 
                    :title="getItemName(item.itemId)"
                    size="small"
                    class="sell-item-card"
                  >
                    <div class="sell-item-info">
                      <div class="item-quantity">
                        拥有数量: {{ item.quantity }}
                      </div>
                      <div class="sell-price">
                        出售价格: {{ getSellPrice(item.itemId) }}灵石/个
                      </div>
                      <div class="sell-actions">
                        <a-input-number
                          v-model:value="sellQuantities[item.itemId]"
                          :min="1"
                          :max="item.quantity"
                          size="small"
                          style="width: 80px; margin-right: 8px;"
                        />
                        <a-button 
                          type="primary"
                          size="small"
                          @click="sellItem(item)"
                        >
                          出售
                        </a-button>
                      </div>
                    </div>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-tab-pane>

        <!-- 委托任务 -->
        <a-tab-pane key="quests" tab="委托任务">
          <div class="quest-section">
            <a-alert 
              message="委托说明" 
              description="完成商会委托可获得丰厚奖励和声望。"
              type="success" 
              show-icon 
              style="margin-bottom: 16px;"
            />

            <div class="quest-list">
              <a-list 
                :data-source="availableQuests"
                item-layout="vertical"
              >
                <template #renderItem="{ item: quest }">
                  <a-list-item>
                    <template #actions>
                      <a-button 
                        v-if="!quest.accepted"
                        type="primary"
                        @click="acceptQuest(quest)"
                      >
                        接受委托
                      </a-button>
                      <a-button 
                        v-else-if="canCompleteQuest(quest)"
                        type="primary"
                        @click="completeQuest(quest)"
                      >
                        完成委托
                      </a-button>
                      <a-tag v-else color="processing">进行中</a-tag>
                    </template>

                    <a-list-item-meta>
                      <template #title>
                        <span class="quest-title">{{ quest.title }}</span>
                        <a-tag :color="getQuestDifficultyColor(quest.difficulty)">
                          {{ quest.difficulty }}
                        </a-tag>
                      </template>
                      <template #description>
                        {{ quest.description }}
                      </template>
                    </a-list-item-meta>

                    <div class="quest-details">
                      <div class="quest-requirements">
                        <strong>需求:</strong>
                        <span v-for="req in quest.requirements" :key="req.itemId">
                          {{ getItemName(req.itemId) }} x{{ req.quantity }}
                          ({{ getItemQuantity(req.itemId) }}/{{ req.quantity }})
                        </span>
                      </div>
                      <div class="quest-rewards">
                        <strong>奖励:</strong>
                        <a-tag v-if="quest.rewards.money" color="gold">
                          {{ quest.rewards.money }}灵石
                        </a-tag>
                        <a-tag v-if="quest.rewards.exp" color="blue">
                          {{ quest.rewards.exp }}经验
                        </a-tag>
                        <a-tag v-if="quest.rewards.reputation" color="purple">
                          {{ quest.rewards.reputation }}声望
                        </a-tag>
                      </div>
                    </div>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { gameStore } from '@/stores/gameStore'

// 商品接口
interface ShopItem {
  id: string
  name: string
  description: string
  category: string
  price: number
  stock?: number
  iconId: string
}

// 委托任务接口
interface Quest {
  id: string
  title: string
  description: string
  difficulty: string
  requirements: { itemId: string; quantity: number }[]
  rewards: { money?: number; exp?: number; reputation?: number }
  accepted: boolean
}

// 响应式数据
const activeTab = ref('shop')
const shopFilter = ref('all')
const searchText = ref('')
const purchaseQuantities = ref<Record<string, number>>({})
const sellQuantities = ref<Record<string, number>>({})
const availableQuests = ref<Quest[]>([])

// 商店物品数据
const shopItems: ShopItem[] = [
  {
    id: 'rare_seed',
    name: '珍稀种子',
    description: '可以种植出珍贵作物的种子',
    category: 'materials',
    price: 200,
    stock: 10,
    iconId: 'seed_rare'
  },
  {
    id: 'crystal_water',
    name: '水晶清泉',
    description: '纯净的水晶泉水，制作高级产品必需',
    category: 'materials',
    price: 150,
    stock: 20,
    iconId: 'water_crystal'
  },
  {
    id: 'workshop_tool',
    name: '精工工具',
    description: '提高工坊效率的精密工具',
    category: 'tools',
    price: 500,
    stock: 5,
    iconId: 'tool_workshop'
  },
  {
    id: 'expansion_permit',
    name: '扩建许可证',
    description: '允许扩建工坊的官方许可',
    category: 'special',
    price: 1000,
    stock: 3,
    iconId: 'permit'
  }
]

// 计算属性
const playerMoney = computed(() => gameStore.playerStatus.value.money)

const filteredShopItems = computed(() => {
  let items = shopItems

  if (shopFilter.value !== 'all') {
    items = items.filter(item => item.category === shopFilter.value)
  }

  if (searchText.value) {
    items = items.filter(item => 
      item.name.includes(searchText.value) || 
      item.description.includes(searchText.value)
    )
  }

  return items
})

const sellableItems = computed(() => {
  return gameStore.inventory.value.filter(item => item.quantity > 0)
})

// 初始化购买数量
onMounted(() => {
  shopItems.forEach(item => {
    purchaseQuantities.value[item.id] = 1
  })
  
  gameStore.inventory.value.forEach(item => {
    sellQuantities.value[item.itemId] = 1
  })

  generateQuests()
})

// 生成委托任务
const generateQuests = () => {
  availableQuests.value = [
    {
      id: 'quest_1',
      title: '收集清泉水',
      description: '商会需要大量清泉水用于制作高级产品',
      difficulty: '简单',
      requirements: [{ itemId: 'spring_water', quantity: 10 }],
      rewards: { money: 500, exp: 100 },
      accepted: false
    },
    {
      id: 'quest_2',
      title: '蜜酿供应',
      description: '为即将到来的庆典准备蜜酿',
      difficulty: '中等',
      requirements: [{ itemId: 'honey_wine', quantity: 5 }],
      rewards: { money: 1000, exp: 200, reputation: 10 },
      accepted: false
    }
  ]
}

// 方法
const canPurchase = (item: ShopItem): boolean => {
  const quantity = purchaseQuantities.value[item.id] || 1
  return playerMoney.value >= item.price * quantity && 
         (!item.stock || quantity <= item.stock)
}

const purchaseItem = (item: ShopItem) => {
  const quantity = purchaseQuantities.value[item.id] || 1
  const totalCost = item.price * quantity

  if (gameStore.spendMoney(totalCost)) {
    gameStore.addItem(item.id, quantity)
    
    if (item.stock) {
      item.stock -= quantity
    }
    
    message.success(`购买成功！获得 ${item.name} x${quantity}`)
  } else {
    message.error('余额不足')
  }
}

const sellItem = (item: any) => {
  const quantity = sellQuantities.value[item.itemId] || 1
  const sellPrice = getSellPrice(item.itemId)
  
  if (gameStore.removeItem(item.itemId, quantity)) {
    gameStore.gainMoney(sellPrice * quantity)
    message.success(`出售成功！获得 ${sellPrice * quantity}灵石`)
  } else {
    message.error('物品数量不足')
  }
}

const getSellPrice = (itemId: string): number => {
  // 简化版本，实际应该从物品配置中获取
  const basePrices: Record<string, number> = {
    'spring_water': 14,
    'flower_honey': 21,
    'spirit_fruit': 35,
    'holy_water': 56,
    'honey_wine': 84,
    'lotion': 140,
    'nectar': 350
  }
  return basePrices[itemId] || 10
}

const getItemName = (itemId: string): string => {
  const names: Record<string, string> = {
    'spring_water': '清泉水',
    'flower_honey': '花蜜',
    'spirit_fruit': '灵果',
    'holy_water': '圣水',
    'honey_wine': '蜜酿',
    'lotion': '乳液',
    'nectar': '琼浆'
  }
  return names[itemId] || itemId
}

const getItemQuantity = (itemId: string): number => {
  return gameStore.getItemQuantity(itemId)
}

const getItemImage = (iconId: string): string => {
  return `/src/assets/graphics/icons/items/${iconId}.png`
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/src/assets/graphics/icons/default.png'
}

const acceptQuest = (quest: Quest) => {
  quest.accepted = true
  message.success(`接受委托: ${quest.title}`)
}

const canCompleteQuest = (quest: Quest): boolean => {
  return quest.requirements.every(req => 
    getItemQuantity(req.itemId) >= req.quantity
  )
}

const completeQuest = (quest: Quest) => {
  if (!canCompleteQuest(quest)) {
    message.error('材料不足，无法完成委托')
    return
  }

  // 消耗材料
  quest.requirements.forEach(req => {
    gameStore.removeItem(req.itemId, req.quantity)
  })

  // 给予奖励
  if (quest.rewards.money) {
    gameStore.gainMoney(quest.rewards.money)
  }
  if (quest.rewards.exp) {
    gameStore.gainExp(quest.rewards.exp)
  }

  message.success(`完成委托: ${quest.title}`)
  
  // 移除已完成的委托
  const index = availableQuests.value.findIndex(q => q.id === quest.id)
  if (index > -1) {
    availableQuests.value.splice(index, 1)
  }
}

const getQuestDifficultyColor = (difficulty: string): string => {
  const colors = {
    '简单': 'green',
    '中等': 'orange',
    '困难': 'red'
  }
  return colors[difficulty] || 'default'
}
</script>

<style scoped>
.jade-guild {
  padding: 16px;
}

.guild-card {
  max-width: 1200px;
  margin: 0 auto;
}

.shop-filters {
  margin-bottom: 16px;
}

.shop-items,
.inventory-items {
  min-height: 400px;
}

.shop-item-card,
.sell-item-card {
  height: 100%;
  transition: all 0.3s ease;
}

.shop-item-card:hover,
.sell-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.item-image {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
}

.item-image img {
  width: 48px;
  height: 48px;
  object-fit: contain;
}

.item-info,
.sell-item-info {
  padding: 8px 0;
}

.item-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.item-price,
.item-quantity,
.sell-price {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.price {
  font-weight: bold;
  color: #faad14;
}

.stock {
  color: #666;
}

.item-actions,
.sell-actions {
  display: flex;
  align-items: center;
}

.quest-section {
  min-height: 400px;
}

.quest-title {
  font-weight: bold;
  margin-right: 8px;
}

.quest-details {
  margin-top: 8px;
}

.quest-requirements,
.quest-rewards {
  margin-bottom: 8px;
}

.quest-requirements span {
  margin-left: 8px;
  margin-right: 16px;
}

.category-materials {
  border-left: 4px solid #52c41a;
}

.category-tools {
  border-left: 4px solid #1890ff;
}

.category-special {
  border-left: 4px solid #722ed1;
}
</style>
