import type {
  GameSave,
  Character,
  Item,
  InventoryItem,
  GameTime,
  PlayerStatus
} from '@/types/game'
import {
  <PERSON>Period,
  Rarity,
  ItemCategory
} from '@/types/game'
import {
  INITIAL_PLAYER_STATUS,
  BASE_ITEMS,
  BASE_SKILLS,
  GAME_VERSION,
  LEVEL_CONFIG
} from '@/constants/game'

// 初始角色数据
export const INITIAL_CHARACTERS: Character[] = [
  {
    id: 'xiaoya',
    name: '小雅',
    description: '温柔善良的少女，是你的第一个伙伴',
    rarity: Rarity.NORMAL,
    level: 1,
    exp: 0,
    maxExp: 100,
    attributes: {
      charm: 15,
      skill: 12,
      stamina: 18,
      wisdom: 10
    },
    isWorking: false,
    storylineCompleted: false
  }
]

// 创建新游戏存档
export function createNewGameSave(): GameSave {
  const now = Date.now()

  return {
    id: `save_${now}`,
    version: GAME_VERSION,
    createdAt: now,
    lastSaved: now,

    gameTime: {
      day: 1,
      period: TimePeriod.MORNING,
      dayOfWeek: 1,
      totalDays: 1
    },

    playerStatus: { ...INITIAL_PLAYER_STATUS },

    characters: [...INITIAL_CHARACTERS],

    inventory: [
      { itemId: 'seed', quantity: 10 },
      { itemId: 'spring_water', quantity: 5 },
      { itemId: 'flower_nectar', quantity: 3 }
    ],

    skills: [...BASE_SKILLS],

    tasks: [],
    events: [],
    farmPlots: Array(9).fill(null).map((_, index) => ({
      id: index,
      isEmpty: true,
      growthLevel: 0,
      isWatered: false,
      isFertilized: false
    })),

    dispatchTasks: [],

    dailyActivities: {
      meal: false,
      bath: false
    },

    unlockedFeatures: {
      doubleRest: false,
      groupRest: false,
      blackMarket: false,
      advancedWorkshops: false
    },

    completedStorylines: [],

    settings: {
      soundVolume: 80,
      musicVolume: 60,
      autoSave: true,
      language: 'zh-CN'
    },

    stats: {
      totalPlayTime: 0,
      charactersRecruited: 1,
      itemsProduced: 0,
      moneyEarned: 0,
      tasksCompleted: 0
    }
  }
}

// 验证存档数据完整性
export function validateGameSave(save: any): save is GameSave {
  if (!save || typeof save !== 'object') return false

  const requiredFields = [
    'id', 'version', 'createdAt', 'lastSaved',
    'gameTime', 'playerStatus', 'characters', 'inventory',
    'skills', 'tasks', 'events', 'farmPlots', 'dispatchTasks',
    'dailyActivities', 'unlockedFeatures', 'completedStorylines',
    'settings', 'stats'
  ]

  return requiredFields.every(field => field in save)
}

// 获取物品信息
export function getItemById(itemId: string): Item | undefined {
  return BASE_ITEMS.find(item => item.id === itemId)
}

// 获取物品名称
export function getItemName(itemId: string): string {
  const item = getItemById(itemId)
  return item ? item.name : '未知物品'
}

// 获取物品价值
export function getItemValue(itemId: string): number {
  const item = getItemById(itemId)
  return item ? item.value : 0
}

// 计算库存中物品的总价值
export function calculateInventoryValue(inventory: InventoryItem[]): number {
  return inventory.reduce((total, invItem) => {
    const itemValue = getItemValue(invItem.itemId)
    return total + (itemValue * invItem.quantity)
  }, 0)
}

// 获取库存中指定物品的数量
export function getInventoryItemQuantity(inventory: InventoryItem[], itemId: string): number {
  const invItem = inventory.find(item => item.itemId === itemId)
  return invItem ? invItem.quantity : 0
}

// 添加物品到库存
export function addItemToInventory(inventory: InventoryItem[], itemId: string, quantity: number): InventoryItem[] {
  const existingItem = inventory.find(item => item.itemId === itemId)

  if (existingItem) {
    existingItem.quantity += quantity
  } else {
    inventory.push({ itemId, quantity })
  }

  return inventory
}

// 从库存中移除物品
export function removeItemFromInventory(inventory: InventoryItem[], itemId: string, quantity: number): boolean {
  const existingItem = inventory.find(item => item.itemId === itemId)

  if (!existingItem || existingItem.quantity < quantity) {
    return false
  }

  existingItem.quantity -= quantity

  if (existingItem.quantity === 0) {
    const index = inventory.indexOf(existingItem)
    inventory.splice(index, 1)
  }

  return true
}

// 检查是否有足够的物品
export function hasEnoughItems(inventory: InventoryItem[], requirements: { itemId: string; quantity: number }[]): boolean {
  return requirements.every(req => {
    const available = getInventoryItemQuantity(inventory, req.itemId)
    return available >= req.quantity
  })
}

// 消耗物品
export function consumeItems(inventory: InventoryItem[], requirements: { itemId: string; quantity: number }[]): boolean {
  if (!hasEnoughItems(inventory, requirements)) {
    return false
  }

  requirements.forEach(req => {
    removeItemFromInventory(inventory, req.itemId, req.quantity)
  })

  return true
}

// 计算角色升级所需经验
export function getExpForNextLevel(level: number): number {
  return LEVEL_CONFIG.getExpForLevel(level + 1)
}

// 检查角色是否可以升级
export function canLevelUp(character: Character): boolean {
  return character.exp >= character.maxExp && character.level < LEVEL_CONFIG.getMaxLevel()
}

// 角色升级
export function levelUpCharacter(character: Character): Character {
  if (!canLevelUp(character)) return character

  const newCharacter = { ...character }
  newCharacter.level += 1
  newCharacter.exp -= newCharacter.maxExp
  newCharacter.maxExp = getExpForNextLevel(newCharacter.level)

  // 属性成长
  const growthRate = 1.1 // 基础成长率
  newCharacter.attributes = {
    charm: Math.floor(newCharacter.attributes.charm * growthRate),
    skill: Math.floor(newCharacter.attributes.skill * growthRate),
    stamina: Math.floor(newCharacter.attributes.stamina * growthRate),
    wisdom: Math.floor(newCharacter.attributes.wisdom * growthRate)
  }

  return newCharacter
}

// 获取稀有度颜色
export function getRarityColor(rarity: Rarity): string {
  const colors = {
    [Rarity.NORMAL]: 'default',
    [Rarity.RARE]: 'blue',
    [Rarity.SPECIAL]: 'purple'
  }
  return colors[rarity]
}

// 获取稀有度文本
export function getRarityText(rarity: Rarity): string {
  const texts = {
    [Rarity.NORMAL]: '普通',
    [Rarity.RARE]: '稀有',
    [Rarity.SPECIAL]: '特殊'
  }
  return texts[rarity]
}

// 获取物品类别颜色
export function getItemCategoryColor(category: ItemCategory): string {
  const colors = {
    [ItemCategory.MATERIALS]: 'blue',
    [ItemCategory.PRODUCTS]: 'green',
    [ItemCategory.SPECIAL]: 'purple'
  }
  return colors[category]
}

// 获取物品类别文本
export function getItemCategoryText(category: ItemCategory): string {
  const texts = {
    [ItemCategory.MATERIALS]: '材料',
    [ItemCategory.PRODUCTS]: '商品',
    [ItemCategory.SPECIAL]: '特殊'
  }
  return texts[category]
}

// 获取时间段文本
export function getTimePeriodText(period: TimePeriod): string {
  const texts = {
    [TimePeriod.MORNING]: '上午',
    [TimePeriod.AFTERNOON]: '下午',
    [TimePeriod.EVENING]: '晚间'
  }
  return texts[period]
}

// 格式化游戏时间显示
export function formatGameTime(gameTime: GameTime): string {
  const weekDays = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日']
  const periodText = getTimePeriodText(gameTime.period)
  return `第${gameTime.day}天 ${weekDays[gameTime.dayOfWeek]} ${periodText}`
}

// 推进游戏时间
export function advanceGameTime(gameTime: GameTime): GameTime {
  const newGameTime = { ...gameTime }

  if (newGameTime.period === TimePeriod.MORNING) {
    newGameTime.period = TimePeriod.AFTERNOON
  } else if (newGameTime.period === TimePeriod.AFTERNOON) {
    newGameTime.period = TimePeriod.EVENING
  } else {
    // 晚间 -> 第二天上午
    newGameTime.period = TimePeriod.MORNING
    newGameTime.day += 1
    newGameTime.totalDays += 1
    newGameTime.dayOfWeek = (newGameTime.dayOfWeek % 7) + 1
  }

  return newGameTime
}

// 检查是否是周六（黑市拍卖会开放）
export function isBlackMarketDay(gameTime: GameTime): boolean {
  return gameTime.dayOfWeek === 6 // 周六
}

// 生成唯一ID
export function generateId(prefix: string = ''): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  return `${prefix}${timestamp}_${random}`
}
