import { ref, computed, reactive } from 'vue'
import type {
  GameSave,
  GameTime,
  PlayerStatus,
  Character,
  InventoryItem,
  DailyActivities,
  UnlockedFeatures,
  Skill
} from '@/types/game'
import { TimePeriod } from '@/types/game'
import { saveManager } from '@/services/saveManager'
import {
  createNewGameSave,
  advanceGameTime,
  formatGameTime,
  addItemToInventory,
  removeItemFromInventory,
  getInventoryItemQuantity
} from '@/utils/gameData'
import { skillStore } from './skillStore'
import { dispatchStore } from './dispatchStore'
import { timeManager, TimeManager } from '@/utils/timeManager'

// 游戏状态管理
export function useGameStore() {
  // 游戏是否已初始化
  const isInitialized = ref(false)

  // 游戏是否正在加载
  const isLoading = ref(false)

  // 当前游戏存档
  const currentSave = ref<GameSave | null>(null)

  // 游戏时间 - 使用时间管理器
  const gameTime = computed(() => timeManager.getCurrentTime())

  // 玩家状态
  const playerStatus = ref<PlayerStatus>({
    energy: 10,
    maxEnergy: 10,
    money: 1000,
    exp: 0,
    level: 1,
    maxExp: 100
  })

  // 角色列表
  const characters = ref<Character[]>([])

  // 库存
  const inventory = ref<InventoryItem[]>([])

  // 每日活动状态
  const dailyActivities = ref<DailyActivities>({
    meal: false,
    bath: false
  })

  // 解锁功能
  const unlockedFeatures = ref<UnlockedFeatures>({
    doubleRest: false,
    groupRest: false,
    blackMarket: false,
    advancedWorkshops: false
  })

  // 计算属性
  const currentTimeDisplay = computed(() => formatGameTime(gameTime.value))

  const canAdvanceTime = computed(() => {
    return gameTime.value.period !== TimePeriod.EVENING && playerStatus.value.energy > 0
  })

  const isBlackMarketOpen = computed(() => {
    return gameTime.value.dayOfWeek === 6 && gameTime.value.period === TimePeriod.AFTERNOON
  })

  const availableCharacters = computed(() => {
    return characters.value.filter(char => !char.isWorking)
  })

  const workingCharacters = computed(() => {
    return characters.value.filter(char => char.isWorking)
  })

  // 初始化游戏
  const initializeGame = async () => {
    if (isInitialized.value) return

    isLoading.value = true

    try {
      await saveManager.init()

      // 尝试加载最新存档
      const latestSave = await saveManager.getLatestSave()
      if (latestSave) {
        loadGameData(latestSave)
      }

      isInitialized.value = true
    } catch (error) {
      console.error('Failed to initialize game:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 开始新游戏
  const startNewGame = async () => {
    isLoading.value = true

    try {
      const newSave = await saveManager.createNewGame()
      loadGameData(newSave)
    } catch (error) {
      console.error('Failed to start new game:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 加载游戏
  const loadGame = async (saveId: string) => {
    isLoading.value = true

    try {
      const save = await saveManager.loadGame(saveId)
      loadGameData(save)
    } catch (error) {
      console.error('Failed to load game:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 保存游戏
  const saveGame = async () => {
    if (!currentSave.value) return

    try {
      // 更新存档数据
      updateSaveData()
      await saveManager.saveCurrentGame()
    } catch (error) {
      console.error('Failed to save game:', error)
      throw error
    }
  }

  // 加载游戏数据到状态
  const loadGameData = (save: GameSave) => {
    currentSave.value = save

    // 初始化时间管理器
    timeManager.deserialize({
      gameTime: save.gameTime,
      dailyActivities: save.dailyActivities
    })

    playerStatus.value = save.playerStatus
    characters.value = save.characters
    inventory.value = save.inventory
    dailyActivities.value = save.dailyActivities
    unlockedFeatures.value = save.unlockedFeatures

    // 加载技能数据
    if (save.skills) {
      skillStore.loadSkills(save.skills, 5) // TODO: 从存档中获取技能点数
    }

    // 加载派遣数据
    if (save.dispatchTasks) {
      dispatchStore.loadData(save.dispatchTasks)
    }
  }

  // 更新存档数据
  const updateSaveData = () => {
    if (!currentSave.value) return

    const timeData = timeManager.serialize()
    saveManager.updateCurrentSave({
      gameTime: timeData.gameTime,
      playerStatus: playerStatus.value,
      characters: characters.value,
      inventory: inventory.value,
      dailyActivities: timeData.dailyActivities,
      unlockedFeatures: unlockedFeatures.value,
      skills: skillStore.skills.value,
      dispatchTasks: [...dispatchStore.activeTasks.value, ...dispatchStore.completedTasks.value]
    })
  }

  // 推进时间
  const advanceGameTimePeriod = () => {
    if (!canAdvanceTime.value) return false

    timeManager.advanceTime()

    // 更新每日活动状态
    dailyActivities.value = timeManager.getDailyActivities()

    // 如果进入新的一天，完全恢复体力
    if (gameTime.value.period === TimePeriod.MORNING) {
      playerStatus.value.energy = playerStatus.value.maxEnergy
    }

    return true
  }

  // 消耗体力
  const consumeEnergy = (amount: number): boolean => {
    if (playerStatus.value.energy < amount) return false

    playerStatus.value.energy -= amount

    // 体力耗尽自动推进到晚间
    if (playerStatus.value.energy <= 0 && gameTime.value.period !== TimePeriod.EVENING) {
      timeManager.setTimePeriod(TimePeriod.EVENING)
    }

    return true
  }

  // 恢复体力
  const restoreEnergy = (amount: number) => {
    playerStatus.value.energy = Math.min(
      playerStatus.value.maxEnergy,
      playerStatus.value.energy + amount
    )
  }

  // 获得金钱
  const gainMoney = (amount: number) => {
    playerStatus.value.money += amount
  }

  // 消费金钱
  const spendMoney = (amount: number): boolean => {
    if (playerStatus.value.money < amount) return false

    playerStatus.value.money -= amount
    return true
  }

  // 获得经验
  const gainExp = (amount: number) => {
    playerStatus.value.exp += amount

    // 检查升级
    while (playerStatus.value.exp >= playerStatus.value.maxExp) {
      levelUp()
    }
  }

  // 升级
  const levelUp = () => {
    playerStatus.value.exp -= playerStatus.value.maxExp
    playerStatus.value.level += 1
    playerStatus.value.maxExp = playerStatus.value.level * 100

    // 升级奖励
    playerStatus.value.maxEnergy += 1
    playerStatus.value.energy = playerStatus.value.maxEnergy
  }

  // 添加物品到库存
  const addItem = (itemId: string, quantity: number) => {
    addItemToInventory(inventory.value, itemId, quantity)
  }

  // 从库存移除物品
  const removeItem = (itemId: string, quantity: number): boolean => {
    return removeItemFromInventory(inventory.value, itemId, quantity)
  }

  // 获取物品数量
  const getItemQuantity = (itemId: string): number => {
    return getInventoryItemQuantity(inventory.value, itemId)
  }

  // 检查是否有足够的物品
  const hasEnoughItems = (requirements: { itemId: string; quantity: number }[]): boolean => {
    return requirements.every(req => getItemQuantity(req.itemId) >= req.quantity)
  }

  // 添加角色
  const addCharacter = (character: Character) => {
    characters.value.push(character)
  }

  // 设置角色工作状态
  const setCharacterWorking = (characterId: string, isWorking: boolean, workLocation?: string) => {
    const character = characters.value.find(char => char.id === characterId)
    if (character) {
      character.isWorking = isWorking
      character.workLocation = workLocation
    }
  }

  // 完成每日活动
  const completeDailyActivity = (activity: keyof DailyActivities) => {
    dailyActivities.value[activity] = true
  }

  // 解锁功能
  const unlockFeature = (feature: keyof UnlockedFeatures) => {
    unlockedFeatures.value[feature] = true
  }

  // 重置游戏状态
  const resetGameState = () => {
    currentSave.value = null
    timeManager.reset()
    playerStatus.value = {
      energy: 10,
      maxEnergy: 10,
      money: 1000,
      exp: 0,
      level: 1,
      maxExp: 100
    }
    characters.value = []
    inventory.value = []
    dailyActivities.value = {
      meal: false,
      bath: false
    }
    unlockedFeatures.value = {
      doubleRest: false,
      groupRest: false,
      blackMarket: false,
      advancedWorkshops: false
    }
  }

  return {
    // 状态
    isInitialized,
    isLoading,
    currentSave,
    gameTime,
    playerStatus,
    characters,
    inventory,
    dailyActivities,
    unlockedFeatures,

    // 计算属性
    currentTimeDisplay,
    canAdvanceTime,
    isBlackMarketOpen,
    availableCharacters,
    workingCharacters,

    // 方法
    initializeGame,
    startNewGame,
    loadGame,
    saveGame,
    advanceGameTimePeriod,
    consumeEnergy,
    restoreEnergy,
    gainMoney,
    spendMoney,
    gainExp,
    addItem,
    removeItem,
    getItemQuantity,
    hasEnoughItems,
    addCharacter,
    setCharacterWorking,
    completeDailyActivity,
    unlockFeature,
    resetGameState
  }
}

// 游戏时间管理器
export function useGameTimeManager() {
  const { gameTime, playerStatus, advanceGameTimePeriod } = gameStore

  let timeInterval: number | null = null
  const timeSpeed = ref(1) // 时间流速倍率

  // 启动时间流逝
  const startTime = () => {
    stopTime()

    timeInterval = window.setInterval(() => {
      // 这里可以添加自动时间推进逻辑
      // 例如：每隔一定时间自动推进游戏时间
    }, 1000 / timeSpeed.value)
  }

  // 停止时间流逝
  const stopTime = () => {
    if (timeInterval) {
      clearInterval(timeInterval)
      timeInterval = null
    }
  }

  // 设置时间流速
  const setTimeSpeed = (speed: number) => {
    timeSpeed.value = speed
    if (timeInterval) {
      startTime() // 重启定时器以应用新速度
    }
  }

  return {
    timeSpeed,
    startTime,
    stopTime,
    setTimeSpeed
  }
}

// 创建全局游戏状态实例
export const gameStore = useGameStore()
export const gameTimeManager = useGameTimeManager()
