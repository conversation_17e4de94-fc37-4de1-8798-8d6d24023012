[{"id": "seed", "name": "灵果种子", "description": "蕴含灵力的种子，可以种植出珍贵的灵果", "category": "material", "rarity": "common", "iconId": "seed_01", "stackable": true, "maxStack": 99, "basePrice": 10, "sellPrice": 5, "effects": [], "unlockConditions": []}, {"id": "spring_water", "name": "清泉水", "description": "来自山泉的纯净水源，制作圣水的必需材料", "category": "material", "rarity": "common", "iconId": "water_01", "stackable": true, "maxStack": 50, "basePrice": 20, "sellPrice": 10, "effects": [], "unlockConditions": []}, {"id": "flower_honey", "name": "花蜜", "description": "采集自百花的甜美蜜糖，制作蜜酿的主要原料", "category": "material", "rarity": "common", "iconId": "honey_01", "stackable": true, "maxStack": 30, "basePrice": 30, "sellPrice": 15, "effects": [], "unlockConditions": []}, {"id": "spirit_fruit", "name": "灵果", "description": "蕴含丰富灵力的果实，可用于制作高级商品", "category": "material", "rarity": "uncommon", "iconId": "fruit_01", "stackable": true, "maxStack": 20, "basePrice": 50, "sellPrice": 25, "effects": [], "unlockConditions": []}, {"id": "rare_herb", "name": "珍稀草药", "description": "极其罕见的草药，制作琼浆的珍贵材料", "category": "material", "rarity": "rare", "iconId": "herb_01", "stackable": true, "maxStack": 10, "basePrice": 200, "sellPrice": 100, "effects": [], "unlockConditions": ["chapter_2_completed"]}]