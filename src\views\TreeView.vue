<template>
  <div class="tree-view">
    <a-card title="发展树" class="tree-card">
      <template #extra>
        <a-space>
          <a-statistic title="可用点数" :value="availablePoints" />
          <a-button type="primary" @click="resetTree" :disabled="!canReset">
            重置发展树
          </a-button>
        </a-space>
      </template>

      <div class="tree-container">
        <div class="tree-branches">
          <!-- 生产分支 -->
          <div class="branch production-branch">
            <h3>生产分支</h3>
            <div class="skill-nodes">
              <div v-for="skill in productionSkills" :key="skill.id" class="skill-node" :class="{
                unlocked: skill.unlocked,
                available: canUnlock(skill),
                locked: !canUnlock(skill) && !skill.unlocked
              }" @click="toggleSkill(skill)">
                <div class="skill-icon">
                  <component :is="skill.icon" />
                </div>
                <div class="skill-info">
                  <div class="skill-name">{{ skill.name }}</div>
                  <div class="skill-description">{{ skill.description }}</div>
                  <div class="skill-cost">消耗: {{ skill.cost }} 点</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 经营分支 -->
          <div class="branch business-branch">
            <h3>经营分支</h3>
            <div class="skill-nodes">
              <div v-for="skill in businessSkills" :key="skill.id" class="skill-node" :class="{
                unlocked: skill.unlocked,
                available: canUnlock(skill),
                locked: !canUnlock(skill) && !skill.unlocked
              }" @click="toggleSkill(skill)">
                <div class="skill-icon">
                  <component :is="skill.icon" />
                </div>
                <div class="skill-info">
                  <div class="skill-name">{{ skill.name }}</div>
                  <div class="skill-description">{{ skill.description }}</div>
                  <div class="skill-cost">消耗: {{ skill.cost }} 点</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 角色分支 -->
          <div class="branch character-branch">
            <h3>角色分支</h3>
            <div class="skill-nodes">
              <div v-for="skill in characterSkills" :key="skill.id" class="skill-node" :class="{
                unlocked: skill.unlocked,
                available: canUnlock(skill),
                locked: !canUnlock(skill) && !skill.unlocked
              }" @click="toggleSkill(skill)">
                <div class="skill-icon">
                  <component :is="skill.icon" />
                </div>
                <div class="skill-info">
                  <div class="skill-name">{{ skill.name }}</div>
                  <div class="skill-description">{{ skill.description }}</div>
                  <div class="skill-cost">消耗: {{ skill.cost }} 点</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 技能详情模态框 -->
    <a-modal v-model:open="detailVisible" :title="selectedSkill?.name" width="500px" :footer="null">
      <div v-if="selectedSkill" class="skill-detail">
        <div class="detail-icon">
          <component :is="selectedSkill.icon" />
        </div>
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="描述">{{ selectedSkill.description }}</a-descriptions-item>
          <a-descriptions-item label="消耗点数">{{ selectedSkill.cost }}</a-descriptions-item>
          <a-descriptions-item label="前置条件">
            <span v-if="selectedSkill.prerequisites.length === 0">无</span>
            <a-tag v-else v-for="prereq in selectedSkill.prerequisites" :key="prereq">
              {{ getSkillName(prereq) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag v-if="selectedSkill.unlocked" color="green">已解锁</a-tag>
            <a-tag v-else-if="canUnlock(selectedSkill)" color="blue">可解锁</a-tag>
            <a-tag v-else color="default">未解锁</a-tag>
          </a-descriptions-item>
        </a-descriptions>

        <div class="skill-effects" style="margin-top: 16px;">
          <h4>效果:</h4>
          <ul>
            <li v-for="effect in selectedSkill.effects" :key="effect">{{ effect }}</li>
          </ul>
        </div>

        <div class="skill-actions" style="margin-top: 16px; text-align: center;">
          <a-button v-if="!selectedSkill.unlocked && canUnlock(selectedSkill)" type="primary"
            @click="unlockSkillHandler(selectedSkill)">
            解锁技能 ({{ selectedSkill.cost }} 点)
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  ExperimentOutlined,
  ShopOutlined,
  UserOutlined,
  GiftOutlined,
  CrownOutlined,
  HeartOutlined,
  StarOutlined,
  ThunderboltOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { skillStore } from '@/stores/skillStore'
import { Skill } from '@/types/game'

const detailVisible = ref(false)
const selectedSkill = ref<Skill | null>(null)

// 从技能管理获取状态
const {
  availablePoints,
  productionSkills,
  businessSkills,
  characterSkills,
  canReset,
  canUnlock,
  unlockSkill,
  resetSkills,
  getSkillName
} = skillStore



// 切换技能状态
const toggleSkill = (skill: Skill) => {
  selectedSkill.value = skill
  detailVisible.value = true
}

// 解锁技能
const unlockSkillHandler = (skill: Skill) => {
  if (!canUnlock(skill)) {
    message.error('无法解锁此技能')
    return
  }

  if (unlockSkill(skill.id)) {
    message.success(`成功解锁技能: ${skill.name}`)
    detailVisible.value = false
  } else {
    message.error('解锁技能失败')
  }
}

// 重置发展树
const resetTree = () => {
  const refundPoints = resetSkills()
  message.success(`发展树已重置，返还 ${refundPoints} 点数`)
}


</script>

<style scoped>
.tree-view {
  height: auto;
  padding: 16px;
}

.tree-card {
  height: 100%;
}

.tree-container {
  height: 100%;
  overflow-y: auto;
}

.tree-branches {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.branch h3 {
  text-align: center;
  margin-bottom: 16px;
  color: #1890ff;
}

.skill-nodes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.skill-node {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.skill-node.unlocked {
  border-color: #52c41a;
  background: #f6ffed;
}

.skill-node.available {
  border-color: #1890ff;
  background: #e6f7ff;
}

.skill-node.locked {
  border-color: #d9d9d9;
  background: #f5f5f5;
  opacity: 0.6;
}

.skill-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.skill-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 50%;
  margin-right: 16px;
}

.skill-info {
  flex: 1;
}

.skill-name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 4px;
}

.skill-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.skill-cost {
  color: #999;
  font-size: 12px;
}

.skill-detail {
  text-align: center;
}

.detail-icon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 50%;
  margin: 0 auto 16px;
}

.skill-effects ul {
  text-align: left;
  margin: 0;
  padding-left: 20px;
}

.skill-effects li {
  margin-bottom: 4px;
}
</style>
