<template>
  <div class="farm-view">
    <a-card title="农场" class="farm-card">
      <template #extra>
        <a-space>
          <a-tag color="green">体力: {{ playerStatus.energy }}/{{ playerStatus.maxEnergy }}</a-tag>
          <a-button @click="goBack">返回地图</a-button>
        </a-space>
      </template>

      <div class="farm-content">
        <!-- 农场操作区 -->
        <a-row gutter={16}>
          <a-col span={6}>
            <a-card hoverable class="action-card" @click="plantSeeds">
              <template #cover>
                <div class="action-icon plant-icon">
                  <EnvironmentOutlined />
                </div>
              </template>
              <a-card-meta title="种植灵果" description="消耗种子种植灵果" />
              <div class="action-info">
                <div>消耗: 1 种子</div>
                <div>拥有: {{ getItemQuantity('seed') }} 个</div>
              </div>
            </a-card>
          </a-col>

          <a-col span={6}>
            <a-card hoverable class="action-card" @click="harvestFruits">
              <template #cover>
                <div class="action-icon harvest-icon">
                  <GiftOutlined />
                </div>
              </template>
              <a-card-meta title="收割灵果" description="收获成熟的灵果" />
              <div class="action-info">
                <div>可收获: {{ matureFruits }} 个</div>
              </div>
            </a-card>
          </a-col>

          <a-col span={6}>
            <a-card hoverable class="action-card" @click="waterPlants">
              <template #cover>
                <div class="action-icon water-icon">
                  <ExperimentOutlined />
                </div>
              </template>
              <a-card-meta title="浇水" description="消耗圣水加速生长" />
              <div class="action-info">
                <div>消耗: 1 圣水</div>
                <div>拥有: {{ getItemQuantity('holy_water') }} 个</div>
              </div>
            </a-card>
          </a-col>

          <a-col span={6}>
            <a-card hoverable class="action-card" @click="fertilize">
              <template #cover>
                <div class="action-icon fertilize-icon">
                  <StarOutlined />
                </div>
              </template>
              <a-card-meta title="施肥" description="消耗乳液提升品质" />
              <div class="action-info">
                <div>消耗: 1 乳液</div>
                <div>拥有: {{ getItemQuantity('lotion') }} 个</div>
              </div>
            </a-card>
          </a-col>
        </a-row>

        <!-- 农田状态 -->
        <a-card title="农田状态" class="field-card" style="margin-top: 24px;">
          <div class="field-grid">
            <div v-for="(plot, index) in farmPlots" :key="index" class="farm-plot" :class="getPlotClass(plot)"
              @click="selectPlot(index)">
              <div class="plot-content">
                <div v-if="plot.isEmpty" class="empty-plot">
                  <PlusOutlined />
                  <div>空地</div>
                </div>
                <div v-else class="planted-plot">
                  <div class="plant-icon">
                    <EnvironmentOutlined />
                  </div>
                  <div class="plant-info">
                    <div class="growth-stage">{{ getGrowthStage(plot.growthLevel) }}</div>
                    <div class="growth-progress">
                      <a-progress :percent="plot.growthLevel" :show-info="false" size="small" />
                    </div>
                    <div v-if="plot.isWatered" class="status-tag">
                      <a-tag color="blue" size="small">已浇水</a-tag>
                    </div>
                    <div v-if="plot.isFertilized" class="status-tag">
                      <a-tag color="green" size="small">已施肥</a-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 收获统计 -->
        <a-card title="今日收获" class="harvest-card" style="margin-top: 24px;">
          <a-row gutter={16}>
            <a-col span={8}>
              <a-statistic title="收获灵果" :value="todayHarvest.fruits" suffix="个" />
            </a-col>
            <a-col span={8}>
              <a-statistic title="获得经验" :value="todayHarvest.exp" suffix="点" />
            </a-col>
            <a-col span={8}>
              <a-statistic title="消耗体力" :value="todayHarvest.energy" suffix="点" />
            </a-col>
          </a-row>
        </a-card>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  EnvironmentOutlined,
  GiftOutlined,
  ExperimentOutlined,
  StarOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { gameStore } from '@/stores/gameStore'

interface FarmPlot {
  isEmpty: boolean
  growthLevel: number // 0-100
  isWatered: boolean
  isFertilized: boolean
  plantedTime?: number
}

const router = useRouter()

// 从状态管理获取玩家状态
const { playerStatus, consumeEnergy, addItem, getItemQuantity } = gameStore

// 农田状态（9块地）
const farmPlots = ref<FarmPlot[]>(Array(9).fill(null).map(() => ({
  isEmpty: true,
  growthLevel: 0,
  isWatered: false,
  isFertilized: false
})))

// 选中的地块
const selectedPlot = ref(-1)

// 今日收获统计
const todayHarvest = ref({
  fruits: 0,
  exp: 0,
  energy: 0
})

// 成熟的果实数量
const matureFruits = computed(() => {
  return farmPlots.value.filter(plot => !plot.isEmpty && plot.growthLevel >= 100).length
})

// 获取地块样式类
const getPlotClass = (plot: FarmPlot) => {
  if (plot.isEmpty) return 'empty'
  if (plot.growthLevel >= 100) return 'mature'
  if (plot.growthLevel >= 50) return 'growing'
  return 'seedling'
}

// 获取生长阶段文本
const getGrowthStage = (level: number) => {
  if (level >= 100) return '成熟'
  if (level >= 75) return '结果期'
  if (level >= 50) return '生长期'
  if (level >= 25) return '发芽期'
  return '种子期'
}

// 选择地块
const selectPlot = (index: number) => {
  selectedPlot.value = index
}

// 种植种子
const plantSeeds = () => {
  if (getItemQuantity('seed') <= 0) {
    message.error('没有种子可以种植')
    return
  }

  if (playerStatus.energy <= 0) {
    message.error('体力不足')
    return
  }

  // 找到第一块空地
  const emptyPlotIndex = farmPlots.value.findIndex(plot => plot.isEmpty)
  if (emptyPlotIndex === -1) {
    message.error('没有空地可以种植')
    return
  }

  // 种植
  farmPlots.value[emptyPlotIndex] = {
    isEmpty: false,
    growthLevel: 10,
    isWatered: false,
    isFertilized: false,
    plantedTime: Date.now()
  }

  gameStore.removeItem('seed', 1)
  consumeEnergy(1)
  todayHarvest.value.energy++

  message.success('种植成功！')

  // 体力耗尽自动返回
  if (playerStatus.energy <= 0) {
    message.warning('体力耗尽，自动返回地图')
    setTimeout(() => goBack(), 1000)
  }
}

// 收割灵果
const harvestFruits = () => {
  if (playerStatus.energy <= 0) {
    message.error('体力不足')
    return
  }

  const maturePlots = farmPlots.value.filter(plot => !plot.isEmpty && plot.growthLevel >= 100)
  if (maturePlots.length === 0) {
    message.error('没有成熟的灵果可以收割')
    return
  }

  // 收割所有成熟的果实
  let harvestedCount = 0
  farmPlots.value.forEach(plot => {
    if (!plot.isEmpty && plot.growthLevel >= 100) {
      plot.isEmpty = true
      plot.growthLevel = 0
      plot.isWatered = false
      plot.isFertilized = false
      harvestedCount++
    }
  })

  // 添加灵果到库存
  addItem('spirit_fruit', harvestedCount)

  consumeEnergy(1)
  todayHarvest.value.fruits += harvestedCount
  todayHarvest.value.exp += harvestedCount * 10
  todayHarvest.value.energy++

  message.success(`收获了 ${harvestedCount} 个灵果！`)

  // 体力耗尽自动返回
  if (playerStatus.energy <= 0) {
    message.warning('体力耗尽，自动返回地图')
    setTimeout(() => goBack(), 1000)
  }
}

// 浇水
const waterPlants = () => {
  if (getItemQuantity('holy_water') <= 0) {
    message.error('没有圣水可以浇水')
    return
  }

  if (playerStatus.energy <= 0) {
    message.error('体力不足')
    return
  }

  // 给所有未成熟的植物浇水
  let wateredCount = 0
  farmPlots.value.forEach(plot => {
    if (!plot.isEmpty && plot.growthLevel < 100 && !plot.isWatered) {
      plot.isWatered = true
      plot.growthLevel = Math.min(100, plot.growthLevel + 20)
      wateredCount++
    }
  })

  if (wateredCount === 0) {
    message.error('没有植物需要浇水')
    return
  }

  gameStore.removeItem('holy_water', 1)
  consumeEnergy(1)
  todayHarvest.value.energy++

  message.success(`浇水完成，加速了 ${wateredCount} 株植物的生长！`)

  // 体力耗尽自动返回
  if (playerStatus.energy <= 0) {
    message.warning('体力耗尽，自动返回地图')
    setTimeout(() => goBack(), 1000)
  }
}

// 施肥
const fertilize = () => {
  if (getItemQuantity('lotion') <= 0) {
    message.error('没有乳液可以施肥')
    return
  }

  if (playerStatus.energy <= 0) {
    message.error('体力不足')
    return
  }

  // 给所有未成熟的植物施肥
  let fertilizedCount = 0
  farmPlots.value.forEach(plot => {
    if (!plot.isEmpty && plot.growthLevel < 100 && !plot.isFertilized) {
      plot.isFertilized = true
      plot.growthLevel = Math.min(100, plot.growthLevel + 30)
      fertilizedCount++
    }
  })

  if (fertilizedCount === 0) {
    message.error('没有植物需要施肥')
    return
  }

  gameStore.removeItem('lotion', 1)
  consumeEnergy(1)
  todayHarvest.value.energy++

  message.success(`施肥完成，大幅提升了 ${fertilizedCount} 株植物的生长！`)

  // 体力耗尽自动返回
  if (playerStatus.energy <= 0) {
    message.warning('体力耗尽，自动返回地图')
    setTimeout(() => goBack(), 1000)
  }
}

const goBack = () => {
  router.push({ name: 'map' })
}

onMounted(() => {
  // 模拟一些已种植的作物
  farmPlots.value[0] = { isEmpty: false, growthLevel: 80, isWatered: true, isFertilized: false }
  farmPlots.value[1] = { isEmpty: false, growthLevel: 100, isWatered: true, isFertilized: true }
  farmPlots.value[2] = { isEmpty: false, growthLevel: 45, isWatered: false, isFertilized: false }
})
</script>

<style scoped>
.farm-view {
  height: auto;
  padding: 16px;
}

.farm-card {
  height: 100%;
}

.farm-content {
  padding: 16px 0;
}

.action-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-icon {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
}

.plant-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.harvest-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.water-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.fertilize-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.action-info {
  margin-top: 12px;
  font-size: 12px;
  color: #666;
}

.field-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.farm-plot {
  aspect-ratio: 1;
  border: 2px solid #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.farm-plot:hover {
  border-color: #1890ff;
  transform: scale(1.02);
}

.farm-plot.empty {
  background: #f5f5f5;
}

.farm-plot.seedling {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  border-color: #1890ff;
}

.farm-plot.growing {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  border-color: #52c41a;
}

.farm-plot.mature {
  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
  border-color: #fa8c16;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px rgba(250, 140, 22, 0.5);
  }

  to {
    box-shadow: 0 0 15px rgba(250, 140, 22, 0.8);
  }
}

.plot-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
}

.empty-plot {
  text-align: center;
  color: #999;
  font-size: 12px;
}

.empty-plot .anticon {
  font-size: 24px;
  margin-bottom: 4px;
}

.planted-plot {
  text-align: center;
  width: 100%;
}

.plant-icon .anticon {
  font-size: 24px;
  color: #52c41a;
  margin-bottom: 4px;
}

.plant-info {
  font-size: 10px;
}

.growth-stage {
  font-weight: bold;
  margin-bottom: 4px;
}

.growth-progress {
  margin-bottom: 4px;
}

.status-tag {
  margin-bottom: 2px;
}
</style>
