import { ref, computed } from 'vue'
import type { DispatchTask, Character } from '@/types/game'
import { WorkshopType } from '@/types/game'
import { WORKSHOP_CONFIG } from '@/constants/game'
import { generateId } from '@/utils/gameData'
import { gameStore } from './gameStore'
import { skillStore } from './skillStore'

// 派遣系统管理
export function useDispatchStore() {
  // 当前派遣任务
  const activeTasks = ref<DispatchTask[]>([])

  // 已完成的派遣任务
  const completedTasks = ref<DispatchTask[]>([])

  // 计算属性
  const availableSlots = computed(() => {
    const slots: Record<WorkshopType, number> = {} as any

    Object.values(WorkshopType).forEach(type => {
      const config = WORKSHOP_CONFIG[type]
      let maxSlots = config.baseSlots

      // 检查技能加成
      if (skillStore.isSkillUnlocked('expand_workshop')) {
        maxSlots += 2
      }
      if (type === WorkshopType.HALL && skillStore.isSkillUnlocked('expand_hall')) {
        maxSlots += 2
      }

      const usedSlots = activeTasks.value
        .filter(task => task.workshopType === type && !task.isCompleted)
        .reduce((total, task) => total + task.characterIds.length, 0)

      slots[type] = maxSlots - usedSlots
    })

    return slots
  })

  const workingCharacters = computed(() => {
    const working = new Set<string>()
    activeTasks.value
      .filter(task => !task.isCompleted)
      .forEach(task => {
        task.characterIds.forEach(id => working.add(id))
      })
    return working
  })

  // 检查工坊是否已解锁
  const isWorkshopUnlocked = (type: WorkshopType): boolean => {
    switch (type) {
      case WorkshopType.HALL:
      case WorkshopType.HOLY_WATER:
      case WorkshopType.HONEY:
        return true
      case WorkshopType.LOTION:
        return skillStore.isSkillUnlocked('lotion_production')
      case WorkshopType.NECTAR:
        return skillStore.isSkillUnlocked('nectar_production')
      default:
        return false
    }
  }

  // 检查是否可以派遣
  const canDispatch = (type: WorkshopType, characterIds: string[]): boolean => {
    if (!isWorkshopUnlocked(type)) return false
    if (characterIds.length === 0) return false
    if (characterIds.length > availableSlots.value[type]) return false

    // 检查角色是否可用
    const unavailableChars = characterIds.filter(id => workingCharacters.value.has(id))
    if (unavailableChars.length > 0) return false

    // 检查材料需求
    const config = WORKSHOP_CONFIG[type]
    if (config.requirements.length > 0) {
      return gameStore.hasEnoughItems(config.requirements)
    }

    return true
  }

  // 派遣角色
  const dispatchCharacters = (type: WorkshopType, characterIds: string[]): boolean => {
    if (!canDispatch(type, characterIds)) return false

    const config = WORKSHOP_CONFIG[type]

    // 消耗材料
    if (config.requirements.length > 0) {
      config.requirements.forEach(req => {
        gameStore.removeItem(req.itemId, req.quantity)
      })
    }

    // 创建派遣任务
    const task: DispatchTask = {
      id: generateId('dispatch_'),
      workshopType: type,
      characterIds: [...characterIds],
      startTime: Date.now(),
      duration: config.duration,
      isCompleted: false,
      rewards: { ...config.rewards }
    }

    activeTasks.value.push(task)

    // 设置角色工作状态
    characterIds.forEach(id => {
      gameStore.setCharacterWorking(id, true, config.name)
    })

    return true
  }

  // 检查任务是否完成
  const checkTaskCompletion = (task: DispatchTask): boolean => {
    const elapsed = Date.now() - task.startTime
    return elapsed >= task.duration
  }

  // 完成派遣任务
  const completeTask = (taskId: string): boolean => {
    const taskIndex = activeTasks.value.findIndex(t => t.id === taskId)
    if (taskIndex === -1) return false

    const task = activeTasks.value[taskIndex]
    if (!checkTaskCompletion(task)) return false

    // 标记任务完成
    task.isCompleted = true

    // 发放奖励
    if (task.rewards.money) {
      gameStore.gainMoney(task.rewards.money)
    }

    if (task.rewards.exp) {
      gameStore.gainExp(task.rewards.exp)

      // 给参与的角色发放经验
      task.characterIds.forEach(charId => {
        const character = gameStore.characters.value.find(c => c.id === charId)
        if (character) {
          character.exp += task.rewards.exp
        }
      })
    }

    if (task.rewards.items) {
      task.rewards.items.forEach(item => {
        gameStore.addItem(item.itemId, item.quantity)
      })
    }

    // 释放角色
    task.characterIds.forEach(id => {
      gameStore.setCharacterWorking(id, false)
    })

    // 移动到已完成列表
    activeTasks.value.splice(taskIndex, 1)
    completedTasks.value.push(task)

    return true
  }

  // 取消派遣任务
  const cancelTask = (taskId: string): boolean => {
    const taskIndex = activeTasks.value.findIndex(t => t.id === taskId)
    if (taskIndex === -1) return false

    const task = activeTasks.value[taskIndex]

    // 释放角色
    task.characterIds.forEach(id => {
      gameStore.setCharacterWorking(id, false)
    })

    // 移除任务
    activeTasks.value.splice(taskIndex, 1)

    return true
  }

  // 获取任务进度
  const getTaskProgress = (task: DispatchTask): number => {
    if (task.isCompleted) return 100

    const elapsed = Date.now() - task.startTime
    const progress = Math.min(100, (elapsed / task.duration) * 100)
    return Math.floor(progress)
  }

  // 获取任务剩余时间
  const getTaskRemainingTime = (task: DispatchTask): number => {
    if (task.isCompleted) return 0

    const elapsed = Date.now() - task.startTime
    const remaining = Math.max(0, task.duration - elapsed)
    return remaining
  }

  // 格式化剩余时间
  const formatRemainingTime = (milliseconds: number): string => {
    const minutes = Math.floor(milliseconds / (1000 * 60))
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000)

    if (minutes > 0) {
      return `${minutes}分${seconds}秒`
    } else {
      return `${seconds}秒`
    }
  }

  // 自动检查并完成任务
  const autoCompleteTask = () => {
    const completableTasks = activeTasks.value.filter(task =>
      !task.isCompleted && checkTaskCompletion(task)
    )

    completableTasks.forEach(task => {
      completeTask(task.id)
    })

    return completableTasks.length
  }

  // 获取工坊状态
  const getWorkshopStatus = (type: WorkshopType) => {
    const config = WORKSHOP_CONFIG[type]
    const tasks = activeTasks.value.filter(t => t.workshopType === type && !t.isCompleted)
    const usedSlots = tasks.reduce((total, task) => total + task.characterIds.length, 0)

    return {
      name: config.name,
      unlocked: isWorkshopUnlocked(type),
      totalSlots: config.baseSlots + (skillStore.isSkillUnlocked('expand_workshop') ? 2 : 0),
      usedSlots,
      availableSlots: availableSlots.value[type],
      activeTasks: tasks
    }
  }

  // 加载派遣数据
  const loadData = (tasks: DispatchTask[]) => {
    activeTasks.value = tasks.filter(t => !t.isCompleted)
    completedTasks.value = tasks.filter(t => t.isCompleted)
  }

  // 重置数据
  const reset = () => {
    activeTasks.value = []
    completedTasks.value = []
  }

  return {
    // 状态
    activeTasks,
    completedTasks,

    // 计算属性
    availableSlots,
    workingCharacters,

    // 方法
    isWorkshopUnlocked,
    canDispatch,
    dispatchCharacters,
    completeTask,
    cancelTask,
    getTaskProgress,
    getTaskRemainingTime,
    formatRemainingTime,
    autoCompleteTask,
    getWorkshopStatus,
    loadData,
    reset
  }
}

// 创建全局派遣状态实例
export const dispatchStore = useDispatchStore()
