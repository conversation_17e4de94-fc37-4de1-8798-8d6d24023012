import { ref, computed } from 'vue'
import type { GameEvent, GameTask } from '@/types/game'
import { EventType, TaskPriority } from '@/types/game'
import { generateId } from '@/utils/gameData'

// 游戏事件管理
export function useEventStore() {
  // 事件列表
  const events = ref<GameEvent[]>([])

  // 任务列表
  const tasks = ref<GameTask[]>([])

  // 事件监听器
  const eventListeners = ref<Map<string, Function[]>>(new Map())

  // 计算属性
  const todayEvents = computed(() => {
    const today = Date.now()
    const todayStart = new Date(today).setHours(0, 0, 0, 0)
    const todayEnd = new Date(today).setHours(23, 59, 59, 999)

    return events.value.filter(event =>
      event.date >= todayStart && event.date <= todayEnd
    )
  })

  const activeTasks = computed(() => {
    return tasks.value.filter(task => !task.completed)
  })

  const completedTasks = computed(() => {
    return tasks.value.filter(task => task.completed)
  })

  const highPriorityTasks = computed(() => {
    return activeTasks.value.filter(task => task.priority === TaskPriority.HIGH)
  })

  const upcomingEvents = computed(() => {
    const now = Date.now()
    return events.value
      .filter(event => event.date > now)
      .sort((a, b) => a.date - b.date)
      .slice(0, 5)
  })

  // 添加事件
  const addEvent = (eventData: Omit<GameEvent, 'id'>) => {
    const event: GameEvent = {
      id: generateId('event_'),
      ...eventData
    }

    events.value.push(event)

    // 触发事件
    emit('eventAdded', event)

    return event
  }

  // 添加任务
  const addTask = (taskData: Omit<GameTask, 'id' | 'createdAt'>) => {
    const task: GameTask = {
      id: generateId('task_'),
      createdAt: Date.now(),
      ...taskData
    }

    tasks.value.push(task)

    // 触发事件
    emit('taskAdded', task)

    return task
  }

  // 完成任务
  const completeTask = (taskId: string) => {
    const task = tasks.value.find(t => t.id === taskId)
    if (!task || task.completed) return false

    task.completed = true

    // 触发事件
    emit('taskCompleted', task)

    // 如果有奖励，触发奖励事件
    if (task.rewards) {
      emit('taskRewards', task.rewards)
    }

    return true
  }

  // 删除任务
  const deleteTask = (taskId: string) => {
    const index = tasks.value.findIndex(t => t.id === taskId)
    if (index === -1) return false

    const task = tasks.value[index]
    tasks.value.splice(index, 1)

    // 触发事件
    emit('taskDeleted', task)

    return true
  }

  // 更新任务
  const updateTask = (taskId: string, updates: Partial<GameTask>) => {
    const task = tasks.value.find(t => t.id === taskId)
    if (!task) return false

    Object.assign(task, updates)

    // 触发事件
    emit('taskUpdated', task)

    return true
  }

  // 标记事件为已完成
  const markEventCompleted = (eventId: string) => {
    const event = events.value.find(e => e.id === eventId)
    if (!event) return false

    event.isCompleted = true

    // 触发事件
    emit('eventCompleted', event)

    return true
  }

  // 删除事件
  const deleteEvent = (eventId: string) => {
    const index = events.value.findIndex(e => e.id === eventId)
    if (index === -1) return false

    const event = events.value[index]
    events.value.splice(index, 1)

    // 触发事件
    emit('eventDeleted', event)

    return true
  }

  // 获取指定日期的事件
  const getEventsByDate = (date: Date) => {
    const dayStart = new Date(date).setHours(0, 0, 0, 0)
    const dayEnd = new Date(date).setHours(23, 59, 59, 999)

    return events.value.filter(event =>
      event.date >= dayStart && event.date <= dayEnd
    )
  }

  // 获取指定类型的事件
  const getEventsByType = (type: EventType) => {
    return events.value.filter(event => event.type === type)
  }

  // 获取过期任务
  const getOverdueTasks = () => {
    const now = Date.now()
    return activeTasks.value.filter(task =>
      task.deadline && task.deadline < now
    )
  }

  // 事件监听
  const on = (eventName: string, callback: Function) => {
    if (!eventListeners.value.has(eventName)) {
      eventListeners.value.set(eventName, [])
    }
    eventListeners.value.get(eventName)!.push(callback)
  }

  // 移除事件监听
  const off = (eventName: string, callback?: Function) => {
    if (!eventListeners.value.has(eventName)) return

    if (callback) {
      const listeners = eventListeners.value.get(eventName)!
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    } else {
      eventListeners.value.delete(eventName)
    }
  }

  // 触发事件
  const emit = (eventName: string, ...args: any[]) => {
    if (!eventListeners.value.has(eventName)) return

    const listeners = eventListeners.value.get(eventName)!
    listeners.forEach(callback => {
      try {
        callback(...args)
      } catch (error) {
        console.error('Event listener error:', error)
      }
    })
  }

  // 创建系统事件
  const createSystemEvent = (title: string, description: string) => {
    return addEvent({
      title,
      description,
      type: EventType.SYSTEM,
      time: new Date().toLocaleTimeString(),
      date: Date.now()
    })
  }

  // 创建故事事件
  const createStoryEvent = (title: string, description: string, date?: number) => {
    return addEvent({
      title,
      description,
      type: EventType.STORY,
      time: new Date(date || Date.now()).toLocaleTimeString(),
      date: date || Date.now()
    })
  }

  // 创建特殊事件
  const createSpecialEvent = (title: string, description: string, date?: number) => {
    return addEvent({
      title,
      description,
      type: EventType.SPECIAL,
      time: new Date(date || Date.now()).toLocaleTimeString(),
      date: date || Date.now()
    })
  }

  // 清理过期事件
  const cleanupOldEvents = (daysToKeep: number = 30) => {
    const cutoffDate = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000)
    const initialLength = events.value.length

    events.value = events.value.filter(event =>
      event.date >= cutoffDate || !event.isCompleted
    )

    const removedCount = initialLength - events.value.length
    if (removedCount > 0) {
      emit('eventsCleanedUp', removedCount)
    }
  }

  // 清理已完成的任务
  const cleanupCompletedTasks = (daysToKeep: number = 7) => {
    const cutoffDate = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000)
    const initialLength = tasks.value.length

    tasks.value = tasks.value.filter(task =>
      !task.completed || task.createdAt >= cutoffDate
    )

    const removedCount = initialLength - tasks.value.length
    if (removedCount > 0) {
      emit('tasksCleanedUp', removedCount)
    }
  }

  // 加载事件和任务数据
  const loadData = (eventsData: GameEvent[], tasksData: GameTask[]) => {
    events.value = eventsData
    tasks.value = tasksData
  }

  // 重置所有数据
  const reset = () => {
    events.value = []
    tasks.value = []
    eventListeners.value.clear()
  }

  return {
    // 状态
    events,
    tasks,

    // 计算属性
    todayEvents,
    activeTasks,
    completedTasks,
    highPriorityTasks,
    upcomingEvents,

    // 方法
    addEvent,
    addTask,
    completeTask,
    deleteTask,
    updateTask,
    markEventCompleted,
    deleteEvent,
    getEventsByDate,
    getEventsByType,
    getOverdueTasks,

    // 事件系统
    on,
    off,
    emit,

    // 便捷方法
    createSystemEvent,
    createStoryEvent,
    createSpecialEvent,

    // 维护方法
    cleanupOldEvents,
    cleanupCompletedTasks,
    loadData,
    reset
  }
}

// 创建全局事件状态实例
export const eventStore = useEventStore()
