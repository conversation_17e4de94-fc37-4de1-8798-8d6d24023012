# 物品数据目录

此目录用于存放游戏物品和商品的JSON数据文件。

## 文件结构

- `materials/` - 材料物品数据
- `products/` - 制作商品数据
- `consumables/` - 消耗品数据
- `special/` - 特殊物品数据

## 物品数据格式

```json
{
  "id": "item_001",
  "name": "物品名称",
  "description": "物品描述",
  "category": "material|product|consumable|special",
  "rarity": "common|uncommon|rare|epic|legendary",
  "iconId": "icon_001",
  "stackable": true,
  "maxStack": 99,
  "basePrice": 100,
  "sellPrice": 50,
  "effects": [],
  "craftingMaterials": [],
  "unlockConditions": []
}
```
