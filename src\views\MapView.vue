<template>
  <div class="map-view">
    <div class="time-display">
      <a-card size="small" class="time-card">
        <template #title>
          <ClockCircleOutlined /> {{ currentTimeDisplay }}
        </template>
        <div class="time-actions">
          <a-button v-if="canAdvanceTime" type="primary" size="small" @click="advanceTime">
            推进时间
          </a-button>
        </div>
      </a-card>
    </div>

    <div class="map-content">
      <!-- 上午时段 - 琉璃胭脂坊 -->
      <div v-if="gameTime.period === TimePeriod.MORNING" class="location-grid">
        <a-card hoverable class="location-card workshop-card" @click="goToWorkshop">
          <template #cover>
            <div class="card-cover workshop-cover">
              <ShopOutlined />
            </div>
          </template>
          <a-card-meta title="琉璃胭脂坊" description="派遣少女进行各种工作" />
        </a-card>
      </div>

      <!-- 下午时段 - 各种场所 -->
      <div v-else-if="gameTime.period === TimePeriod.AFTERNOON" class="location-grid">
        <a-card hoverable class="location-card market-card" @click="goToMarket">
          <template #cover>
            <div class="card-cover market-cover">
              <ShoppingOutlined />
            </div>
          </template>
          <a-card-meta title="翡翠商会" description="购买材料，出售商品" />
        </a-card>

        <a-card hoverable class="location-card garden-card" @click="goToGarden">
          <template #cover>
            <div class="card-cover garden-cover">
              <UserAddOutlined />
            </div>
          </template>
          <a-card-meta title="孤独园" description="招募新的少女" />
        </a-card>

        <a-card hoverable class="location-card farm-card" @click="goToFarm">
          <template #cover>
            <div class="card-cover farm-cover">
              <EnvironmentOutlined />
            </div>
          </template>
          <a-card-meta title="农场" description="种植和收获灵果" />
        </a-card>

        <!-- 黑市拍卖会（仅周六开放） -->
        <a-card v-if="gameTime.dayOfWeek === 6" hoverable class="location-card auction-card" @click="goToAuction">
          <template #cover>
            <div class="card-cover auction-cover">
              <CrownOutlined />
            </div>
          </template>
          <a-card-meta title="黑市拍卖会" description="购买稀有少女和物品" />
        </a-card>
      </div>

      <!-- 晚间时段 - 寝室 -->
      <div v-else-if="gameTime.period === TimePeriod.EVENING" class="location-grid">
        <a-card hoverable class="location-card bedroom-card" @click="goToBedroom">
          <template #cover>
            <div class="card-cover bedroom-cover">
              <HomeOutlined />
            </div>
          </template>
          <a-card-meta title="寝室" description="用餐、洗澡、休息" />
        </a-card>
      </div>
    </div>

    <!-- 体力不足提示 -->
    <a-alert v-if="playerStatus.energy <= 0" message="体力不足" description="体力耗尽，自动进入晚间休息时段" type="warning" show-icon
      class="energy-alert" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  ClockCircleOutlined,
  ShopOutlined,
  ShoppingOutlined,
  UserAddOutlined,
  EnvironmentOutlined,
  CrownOutlined,
  HomeOutlined
} from '@ant-design/icons-vue'
import { gameStore } from '@/stores/gameStore'
import { TimePeriod } from '@/types/game'
import { message } from 'ant-design-vue'

const router = useRouter()

// 从状态管理获取游戏状态
const {
  gameTime,
  playerStatus,
  currentTimeDisplay,
  canAdvanceTime,
  isBlackMarketOpen,
  advanceGameTimePeriod,
  saveGame
} = gameStore

// 推进时间
const advanceTime = async () => {
  if (advanceGameTimePeriod()) {
    message.success('时间推进成功')
    // 自动保存
    try {
      await saveGame()
    } catch (error) {
      console.error('Auto save failed:', error)
    }
  } else {
    message.warning('无法推进时间')
  }
}

// 导航方法
const goToWorkshop = () => router.push({ name: 'workshop' })
const goToMarket = () => router.push({ name: 'market' })
const goToGarden = () => router.push({ name: 'garden' })
const goToFarm = () => router.push({ name: 'farm' })
const goToAuction = () => router.push({ name: 'auction' })
const goToBedroom = () => router.push({ name: 'bedroom' })
</script>

<style scoped>
.map-view {
  height: 100%;
  padding: 16px;
}

.time-display {
  margin-bottom: 20px;
}

.time-card {
  max-width: 300px;
  margin: 0 auto;
}

.time-actions {
  text-align: center;
  margin-top: 8px;
}

.map-content {
  flex: 1;
}

.location-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.location-card {
  transition: all 0.3s ease;
}

.location-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-cover {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.workshop-cover {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.market-cover {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.garden-cover {
  background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

.farm-cover {
  background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
}

.auction-cover {
  background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
}

.bedroom-cover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.energy-alert {
  margin-top: 20px;
}
</style>
