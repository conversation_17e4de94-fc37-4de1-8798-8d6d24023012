<template>
  <div class="workshop-view">
    <a-card title="琉璃胭脂坊" class="workshop-card">
      <template #extra>
        <a-button @click="goBack">返回地图</a-button>
      </template>

      <a-row gutter={16}>
        <!-- 会客大厅 -->
        <a-col span={12}>
          <a-card hoverable class="workshop-section" @click="enterHall">
            <template #cover>
              <div class="section-cover hall-cover">
                <UserOutlined />
              </div>
            </template>
            <a-card-meta title="会客大厅" description="派遣少女进行歌舞曲艺表演" />
            <div class="section-info">
              <a-tag color="blue">可派遣: 2人</a-tag>
              <a-tag color="green">收益: 灵石</a-tag>
            </div>
          </a-card>
        </a-col>

        <!-- 圣水工坊 -->
        <a-col span={12}>
          <a-card hoverable class="workshop-section" @click="enterHolyWater">
            <template #cover>
              <div class="section-cover holy-water-cover">
                <ExperimentOutlined />
              </div>
            </template>
            <a-card-meta title="圣水工坊" description="生产圣水，需要消耗材料" />
            <div class="section-info">
              <a-tag color="blue">可派遣: 2人</a-tag>
              <a-tag color="orange">需要: 材料</a-tag>
            </div>
          </a-card>
        </a-col>

        <!-- 蜜酿工坊 -->
        <a-col span={12}>
          <a-card hoverable class="workshop-section" @click="enterHoney">
            <template #cover>
              <div class="section-cover honey-cover">
                <GiftOutlined />
              </div>
            </template>
            <a-card-meta title="蜜酿工坊" description="生产蜜酿，需要消耗材料" />
            <div class="section-info">
              <a-tag color="blue">可派遣: 2人</a-tag>
              <a-tag color="orange">需要: 材料</a-tag>
            </div>
          </a-card>
        </a-col>

        <!-- 乳液工坊 -->
        <a-col span={12}>
          <a-card hoverable class="workshop-section" :class="{ disabled: !isLotionUnlocked }" @click="enterLotion">
            <template #cover>
              <div class="section-cover lotion-cover">
                <StarOutlined />
              </div>
            </template>
            <a-card-meta title="乳液工坊" description="生产乳液，需要解锁发展树" />
            <div class="section-info">
              <a-tag v-if="isLotionUnlocked" color="blue">可派遣: 2人</a-tag>
              <a-tag v-else color="default">未解锁</a-tag>
              <a-tag color="orange">需要: 材料</a-tag>
            </div>
          </a-card>
        </a-col>

        <!-- 琼浆工坊 -->
        <a-col span={12}>
          <a-card hoverable class="workshop-section" :class="{ disabled: !isNectarUnlocked }" @click="enterNectar">
            <template #cover>
              <div class="section-cover nectar-cover">
                <CrownOutlined />
              </div>
            </template>
            <a-card-meta title="琼浆工坊" description="生产琼浆，需要解锁发展树" />
            <div class="section-info">
              <a-tag v-if="isNectarUnlocked" color="blue">可派遣: 2人</a-tag>
              <a-tag v-else color="default">未解锁</a-tag>
              <a-tag color="orange">需要: 材料</a-tag>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  UserOutlined,
  ExperimentOutlined,
  GiftOutlined,
  StarOutlined,
  CrownOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const router = useRouter()

// 工坊解锁状态（后续从状态管理获取）
const isLotionUnlocked = ref(false)
const isNectarUnlocked = ref(false)

const goBack = () => {
  router.push({ name: 'map' })
}

const enterHall = () => {
  router.push({ name: 'hall' })
}

const enterHolyWater = () => {
  router.push({ name: 'holy-water' })
}

const enterHoney = () => {
  router.push({ name: 'honey' })
}

const enterLotion = () => {
  if (!isLotionUnlocked.value) {
    message.warning('请先在发展树中解锁乳液制作')
    return
  }
  message.info('进入乳液工坊功能开发中...')
}

const enterNectar = () => {
  if (!isNectarUnlocked.value) {
    message.warning('请先在发展树中解锁琼浆制作')
    return
  }
  message.info('进入琼浆工坊功能开发中...')
}
</script>

<style scoped>
.workshop-view {
  height: auto;
  padding: 16px;
}

.workshop-card {
  height: 100%;
}

.workshop-section {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.workshop-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.workshop-section.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.workshop-section.disabled:hover {
  transform: none;
  box-shadow: none;
}

.section-cover {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: white;
}

.hall-cover {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.holy-water-cover {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.honey-cover {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.lotion-cover {
  background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

.nectar-cover {
  background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
}

.section-info {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
