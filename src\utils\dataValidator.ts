import type { GameSave, Character, InventoryItem, PlayerStatus, GameTime } from '@/types/game'
import { TimePeriod, Rarity } from '@/types/game'

// 数据验证器
export class DataValidator {
  // 验证玩家状态
  static validatePlayerStatus(status: any): PlayerStatus | null {
    if (!status || typeof status !== 'object') return null

    try {
      return {
        energy: Math.max(0, Math.min(status.maxEnergy || 10, status.energy || 0)),
        maxEnergy: Math.max(1, status.maxEnergy || 10),
        money: Math.max(0, status.money || 0),
        exp: Math.max(0, status.exp || 0),
        level: Math.max(1, status.level || 1),
        maxExp: Math.max(100, status.maxExp || 100)
      }
    } catch (error) {
      console.error('Player status validation failed:', error)
      return null
    }
  }

  // 验证游戏时间
  static validateGameTime(time: any): GameTime | null {
    if (!time || typeof time !== 'object') return null

    try {
      const period = Object.values(TimePeriod).includes(time.period) 
        ? time.period 
        : TimePeriod.MORNING

      return {
        day: Math.max(1, time.day || 1),
        period,
        dayOfWeek: Math.max(1, Math.min(7, time.dayOfWeek || 1)),
        totalDays: Math.max(1, time.totalDays || 1)
      }
    } catch (error) {
      console.error('Game time validation failed:', error)
      return null
    }
  }

  // 验证角色数据
  static validateCharacter(character: any): Character | null {
    if (!character || typeof character !== 'object') return null

    try {
      const rarity = Object.values(Rarity).includes(character.rarity) 
        ? character.rarity 
        : Rarity.NORMAL

      return {
        id: character.id || '',
        name: character.name || '未知角色',
        description: character.description || '',
        rarity,
        portraitId: character.portraitId || character.avatar,
        level: Math.max(1, character.level || 1),
        exp: Math.max(0, character.exp || 0),
        maxExp: Math.max(100, character.maxExp || 100),
        attributes: {
          charm: Math.max(0, character.attributes?.charm || 10),
          skill: Math.max(0, character.attributes?.skill || 10),
          stamina: Math.max(0, character.attributes?.stamina || 10),
          wisdom: Math.max(0, character.attributes?.wisdom || 10)
        },
        isWorking: Boolean(character.isWorking),
        workLocation: character.workLocation,
        unlockCondition: character.unlockCondition,
        storylineCompleted: Boolean(character.storylineCompleted)
      }
    } catch (error) {
      console.error('Character validation failed:', error)
      return null
    }
  }

  // 验证库存物品
  static validateInventoryItem(item: any): InventoryItem | null {
    if (!item || typeof item !== 'object') return null

    try {
      return {
        itemId: item.itemId || '',
        quantity: Math.max(0, item.quantity || 0)
      }
    } catch (error) {
      console.error('Inventory item validation failed:', error)
      return null
    }
  }

  // 验证完整的游戏存档
  static validateGameSave(save: any): GameSave | null {
    if (!save || typeof save !== 'object') return null

    try {
      const playerStatus = this.validatePlayerStatus(save.playerStatus)
      const gameTime = this.validateGameTime(save.gameTime)

      if (!playerStatus || !gameTime) return null

      const characters = Array.isArray(save.characters) 
        ? save.characters.map(char => this.validateCharacter(char)).filter(Boolean)
        : []

      const inventory = Array.isArray(save.inventory)
        ? save.inventory.map(item => this.validateInventoryItem(item)).filter(Boolean)
        : []

      return {
        id: save.id || '',
        name: save.name || '未命名存档',
        version: save.version || '1.0.0',
        createdAt: save.createdAt || Date.now(),
        lastSaved: save.lastSaved || Date.now(),
        gameTime,
        playerStatus,
        characters,
        inventory,
        dailyActivities: {
          meal: Boolean(save.dailyActivities?.meal),
          bath: Boolean(save.dailyActivities?.bath)
        },
        unlockedFeatures: {
          doubleRest: Boolean(save.unlockedFeatures?.doubleRest),
          groupRest: Boolean(save.unlockedFeatures?.groupRest),
          blackMarket: Boolean(save.unlockedFeatures?.blackMarket),
          advancedWorkshops: Boolean(save.unlockedFeatures?.advancedWorkshops)
        },
        skills: save.skills || [],
        dispatchTasks: save.dispatchTasks || []
      }
    } catch (error) {
      console.error('Game save validation failed:', error)
      return null
    }
  }
}

// 数据同步管理器
export class DataSyncManager {
  private static instance: DataSyncManager
  private syncQueue: Array<() => Promise<void>> = []
  private isSyncing = false
  private lastSyncTime = 0
  private syncInterval = 5000 // 5秒同步间隔

  static getInstance(): DataSyncManager {
    if (!DataSyncManager.instance) {
      DataSyncManager.instance = new DataSyncManager()
    }
    return DataSyncManager.instance
  }

  // 添加同步任务
  addSyncTask(task: () => Promise<void>): void {
    this.syncQueue.push(task)
    this.processSyncQueue()
  }

  // 处理同步队列
  private async processSyncQueue(): Promise<void> {
    if (this.isSyncing || this.syncQueue.length === 0) return

    const now = Date.now()
    if (now - this.lastSyncTime < this.syncInterval) {
      // 延迟执行
      setTimeout(() => this.processSyncQueue(), this.syncInterval - (now - this.lastSyncTime))
      return
    }

    this.isSyncing = true
    this.lastSyncTime = now

    try {
      while (this.syncQueue.length > 0) {
        const task = this.syncQueue.shift()
        if (task) {
          await task()
        }
      }
    } catch (error) {
      console.error('Sync task failed:', error)
    } finally {
      this.isSyncing = false
    }
  }

  // 强制同步
  async forceSync(): Promise<void> {
    this.lastSyncTime = 0
    await this.processSyncQueue()
  }

  // 清空同步队列
  clearSyncQueue(): void {
    this.syncQueue = []
  }
}

// 数据完整性检查器
export class DataIntegrityChecker {
  // 检查角色数据完整性
  static checkCharacterIntegrity(characters: Character[]): {
    valid: Character[]
    invalid: any[]
    duplicates: string[]
  } {
    const valid: Character[] = []
    const invalid: any[] = []
    const duplicates: string[] = []
    const seenIds = new Set<string>()

    characters.forEach(character => {
      if (seenIds.has(character.id)) {
        duplicates.push(character.id)
        return
      }

      const validatedCharacter = DataValidator.validateCharacter(character)
      if (validatedCharacter) {
        valid.push(validatedCharacter)
        seenIds.add(character.id)
      } else {
        invalid.push(character)
      }
    })

    return { valid, invalid, duplicates }
  }

  // 检查库存数据完整性
  static checkInventoryIntegrity(inventory: InventoryItem[]): {
    valid: InventoryItem[]
    invalid: any[]
    consolidated: InventoryItem[]
  } {
    const valid: InventoryItem[] = []
    const invalid: any[] = []
    const itemMap = new Map<string, number>()

    inventory.forEach(item => {
      const validatedItem = DataValidator.validateInventoryItem(item)
      if (validatedItem && validatedItem.quantity > 0) {
        valid.push(validatedItem)
        
        // 合并相同物品
        const currentQuantity = itemMap.get(validatedItem.itemId) || 0
        itemMap.set(validatedItem.itemId, currentQuantity + validatedItem.quantity)
      } else {
        invalid.push(item)
      }
    })

    const consolidated = Array.from(itemMap.entries()).map(([itemId, quantity]) => ({
      itemId,
      quantity
    }))

    return { valid, invalid, consolidated }
  }

  // 修复游戏存档
  static repairGameSave(save: any): GameSave | null {
    const validatedSave = DataValidator.validateGameSave(save)
    if (!validatedSave) return null

    // 修复角色数据
    const characterCheck = this.checkCharacterIntegrity(validatedSave.characters)
    validatedSave.characters = characterCheck.valid

    // 修复库存数据
    const inventoryCheck = this.checkInventoryIntegrity(validatedSave.inventory)
    validatedSave.inventory = inventoryCheck.consolidated

    // 记录修复信息
    if (characterCheck.invalid.length > 0 || characterCheck.duplicates.length > 0) {
      console.warn('Character data repaired:', {
        invalidCount: characterCheck.invalid.length,
        duplicateCount: characterCheck.duplicates.length
      })
    }

    if (inventoryCheck.invalid.length > 0) {
      console.warn('Inventory data repaired:', {
        invalidCount: inventoryCheck.invalid.length,
        consolidatedCount: inventoryCheck.consolidated.length
      })
    }

    return validatedSave
  }
}

// 创建全局实例
export const dataValidator = DataValidator
export const dataSyncManager = DataSyncManager.getInstance()
export const dataIntegrityChecker = DataIntegrityChecker
