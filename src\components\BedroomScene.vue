<template>
  <div class="bedroom-scene">
    <a-card title="寝室" class="bedroom-card">
      <template #extra>
        <a-tag color="purple">晚间时光</a-tag>
      </template>

      <div class="bedroom-content">
        <!-- 时间显示 -->
        <div class="time-display">
          <a-statistic 
            title="当前时间" 
            :value="timeDisplay"
            class="time-stat"
          />
        </div>

        <!-- 活动选项 -->
        <div class="activity-options">
          <a-row :gutter="[16, 16]">
            <!-- 用餐 -->
            <a-col :xs="24" :sm="8">
              <a-card 
                title="用餐" 
                size="small"
                class="activity-card"
                :class="{ 'completed': dailyActivities.meal }"
                hoverable
                @click="showMealDialog"
              >
                <template #cover>
                  <div class="activity-icon">
                    <CoffeeOutlined />
                  </div>
                </template>
                <div class="activity-info">
                  <p class="activity-description">享用美味晚餐，恢复体力</p>
                  <div class="activity-status">
                    <a-tag v-if="dailyActivities.meal" color="green">已完成</a-tag>
                    <a-tag v-else color="orange">未完成</a-tag>
                  </div>
                  <div class="activity-effects">
                    <span class="effect">体力 +3</span>
                  </div>
                </div>
              </a-card>
            </a-col>

            <!-- 洗澡 -->
            <a-col :xs="24" :sm="8">
              <a-card 
                title="洗澡" 
                size="small"
                class="activity-card"
                :class="{ 'completed': dailyActivities.bath }"
                hoverable
                @click="showBathDialog"
              >
                <template #cover>
                  <div class="activity-icon">
                    <ExperimentOutlined />
                  </div>
                </template>
                <div class="activity-info">
                  <p class="activity-description">舒缓身心，提升魅力</p>
                  <div class="activity-status">
                    <a-tag v-if="dailyActivities.bath" color="green">已完成</a-tag>
                    <a-tag v-else color="orange">未完成</a-tag>
                  </div>
                  <div class="activity-effects">
                    <span class="effect">魅力 +1</span>
                  </div>
                </div>
              </a-card>
            </a-col>

            <!-- 睡觉 -->
            <a-col :xs="24" :sm="8">
              <a-card 
                title="睡觉" 
                size="small"
                class="activity-card sleep-card"
                hoverable
                @click="showSleepDialog"
              >
                <template #cover>
                  <div class="activity-icon">
                    <MoonOutlined />
                  </div>
                </template>
                <div class="activity-info">
                  <p class="activity-description">结束一天，进入新的一天</p>
                  <div class="activity-effects">
                    <span class="effect">推进到明天</span>
                    <span class="effect">完全恢复体力</span>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 今日总结 -->
        <div class="daily-summary">
          <a-divider>今日总结</a-divider>
          <a-descriptions :column="2" bordered size="small">
            <a-descriptions-item label="获得经验">{{ todayStats.exp }}</a-descriptions-item>
            <a-descriptions-item label="获得金钱">{{ todayStats.money }}</a-descriptions-item>
            <a-descriptions-item label="完成任务">{{ todayStats.tasks }}</a-descriptions-item>
            <a-descriptions-item label="制作物品">{{ todayStats.items }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 角色状态 -->
        <div class="character-status">
          <a-divider>角色状态</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-statistic title="当前体力" :value="playerStatus.energy" :suffix="`/ ${playerStatus.maxEnergy}`" />
            </a-col>
            <a-col :span="12">
              <a-statistic title="当前金钱" :value="playerStatus.money" suffix="灵石" />
            </a-col>
          </a-row>
        </div>
      </div>
    </a-card>

    <!-- 用餐对话框 -->
    <a-modal 
      v-model:open="mealDialogVisible" 
      title="用餐" 
      :footer="null"
      width="500px"
    >
      <div class="meal-dialog">
        <p>选择今晚的晚餐：</p>
        <div class="meal-options">
          <a-radio-group v-model:value="selectedMeal" class="meal-radio-group">
            <a-radio 
              v-for="meal in mealOptions" 
              :key="meal.id"
              :value="meal.id"
              class="meal-option"
            >
              <div class="meal-info">
                <span class="meal-name">{{ meal.name }}</span>
                <span class="meal-effect">{{ meal.effect }}</span>
                <span class="meal-cost" v-if="meal.cost">{{ meal.cost }}灵石</span>
              </div>
            </a-radio>
          </a-radio-group>
        </div>
        <div class="dialog-actions">
          <a-button @click="mealDialogVisible = false">取消</a-button>
          <a-button type="primary" @click="confirmMeal" :disabled="!selectedMeal">确认</a-button>
        </div>
      </div>
    </a-modal>

    <!-- 洗澡对话框 -->
    <a-modal 
      v-model:open="bathDialogVisible" 
      title="洗澡" 
      :footer="null"
      width="500px"
    >
      <div class="bath-dialog">
        <p>选择洗澡方式：</p>
        <div class="bath-options">
          <a-radio-group v-model:value="selectedBath" class="bath-radio-group">
            <a-radio 
              v-for="bath in bathOptions" 
              :key="bath.id"
              :value="bath.id"
              class="bath-option"
            >
              <div class="bath-info">
                <span class="bath-name">{{ bath.name }}</span>
                <span class="bath-effect">{{ bath.effect }}</span>
                <span class="bath-cost" v-if="bath.cost">{{ bath.cost }}灵石</span>
              </div>
            </a-radio>
          </a-radio-group>
        </div>
        <div class="dialog-actions">
          <a-button @click="bathDialogVisible = false">取消</a-button>
          <a-button type="primary" @click="confirmBath" :disabled="!selectedBath">确认</a-button>
        </div>
      </div>
    </a-modal>

    <!-- 睡觉对话框 -->
    <a-modal 
      v-model:open="sleepDialogVisible" 
      title="睡觉" 
      :footer="null"
      width="500px"
    >
      <div class="sleep-dialog">
        <a-alert 
          message="确认睡觉？" 
          description="睡觉将会结束今天，进入新的一天。所有体力将完全恢复，但未完成的每日活动将重置。"
          type="warning" 
          show-icon 
          style="margin-bottom: 16px;"
        />
        
        <div class="sleep-summary">
          <h4>今日完成情况：</h4>
          <ul>
            <li>用餐：{{ dailyActivities.meal ? '已完成' : '未完成' }}</li>
            <li>洗澡：{{ dailyActivities.bath ? '已完成' : '未完成' }}</li>
          </ul>
        </div>

        <div class="dialog-actions">
          <a-button @click="sleepDialogVisible = false">取消</a-button>
          <a-button type="primary" @click="confirmSleep">确认睡觉</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { CoffeeOutlined, ExperimentOutlined, MoonOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { gameStore } from '@/stores/gameStore'
import { timeManager } from '@/utils/timeManager'

// 用餐选项
interface MealOption {
  id: string
  name: string
  effect: string
  energyRestore: number
  cost?: number
}

// 洗澡选项
interface BathOption {
  id: string
  name: string
  effect: string
  charmBonus: number
  cost?: number
}

// 响应式数据
const mealDialogVisible = ref(false)
const bathDialogVisible = ref(false)
const sleepDialogVisible = ref(false)
const selectedMeal = ref('')
const selectedBath = ref('')
const todayStats = ref({
  exp: 0,
  money: 0,
  tasks: 0,
  items: 0
})

// 用餐选项
const mealOptions: MealOption[] = [
  {
    id: 'simple',
    name: '简单晚餐',
    effect: '恢复3点体力',
    energyRestore: 3
  },
  {
    id: 'deluxe',
    name: '丰盛晚餐',
    effect: '恢复5点体力',
    energyRestore: 5,
    cost: 50
  },
  {
    id: 'feast',
    name: '豪华盛宴',
    effect: '恢复8点体力',
    energyRestore: 8,
    cost: 150
  }
]

// 洗澡选项
const bathOptions: BathOption[] = [
  {
    id: 'simple',
    name: '简单清洗',
    effect: '魅力+1',
    charmBonus: 1
  },
  {
    id: 'herb',
    name: '草药浴',
    effect: '魅力+2',
    charmBonus: 2,
    cost: 30
  },
  {
    id: 'luxury',
    name: '奢华香浴',
    effect: '魅力+3',
    charmBonus: 3,
    cost: 100
  }
]

// 计算属性
const timeDisplay = computed(() => timeManager.getTimeDisplayText())
const playerStatus = computed(() => gameStore.playerStatus.value)
const dailyActivities = computed(() => gameStore.dailyActivities.value)

// 方法
const showMealDialog = () => {
  if (dailyActivities.value.meal) {
    message.info('今天已经用过餐了')
    return
  }
  selectedMeal.value = ''
  mealDialogVisible.value = true
}

const showBathDialog = () => {
  if (dailyActivities.value.bath) {
    message.info('今天已经洗过澡了')
    return
  }
  selectedBath.value = ''
  bathDialogVisible.value = true
}

const showSleepDialog = () => {
  sleepDialogVisible.value = true
}

const confirmMeal = () => {
  const meal = mealOptions.find(m => m.id === selectedMeal.value)
  if (!meal) return

  // 检查费用
  if (meal.cost && !gameStore.spendMoney(meal.cost)) {
    message.error('金钱不足')
    return
  }

  // 恢复体力
  gameStore.restoreEnergy(meal.energyRestore)
  
  // 标记活动完成
  gameStore.completeDailyActivity('meal')
  
  message.success(`享用了${meal.name}，恢复了${meal.energyRestore}点体力`)
  mealDialogVisible.value = false
}

const confirmBath = () => {
  const bath = bathOptions.find(b => b.id === selectedBath.value)
  if (!bath) return

  // 检查费用
  if (bath.cost && !gameStore.spendMoney(bath.cost)) {
    message.error('金钱不足')
    return
  }

  // 提升魅力（这里简化处理，实际应该影响角色属性）
  // 可以给所有角色增加魅力
  gameStore.characters.value.forEach(character => {
    character.attributes.charm += bath.charmBonus
  })
  
  // 标记活动完成
  gameStore.completeDailyActivity('bath')
  
  message.success(`完成了${bath.name}，所有角色魅力+${bath.charmBonus}`)
  bathDialogVisible.value = false
}

const confirmSleep = () => {
  // 推进到下一天
  gameStore.advanceGameTimePeriod()
  
  message.success('新的一天开始了！')
  sleepDialogVisible.value = false
  
  // 重置今日统计
  resetTodayStats()
}

const resetTodayStats = () => {
  todayStats.value = {
    exp: 0,
    money: 0,
    tasks: 0,
    items: 0
  }
}

// 生命周期
onMounted(() => {
  // 初始化今日统计（这里简化处理）
  todayStats.value = {
    exp: Math.floor(Math.random() * 200),
    money: Math.floor(Math.random() * 500),
    tasks: Math.floor(Math.random() * 5),
    items: Math.floor(Math.random() * 10)
  }
})
</script>

<style scoped>
.bedroom-scene {
  padding: 16px;
}

.bedroom-card {
  max-width: 800px;
  margin: 0 auto;
}

.bedroom-content {
  min-height: 500px;
}

.time-display {
  text-align: center;
  margin-bottom: 24px;
}

.time-stat {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  border-radius: 8px;
}

.activity-options {
  margin-bottom: 24px;
}

.activity-card {
  height: 100%;
  transition: all 0.3s ease;
  cursor: pointer;
}

.activity-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.activity-card.completed {
  background-color: #f6ffed;
  border-color: #52c41a;
}

.sleep-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.activity-icon {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
}

.sleep-card .activity-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.activity-info {
  padding: 8px 0;
}

.activity-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.sleep-card .activity-description {
  color: rgba(255, 255, 255, 0.8);
}

.activity-status {
  margin-bottom: 8px;
}

.activity-effects {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.effect {
  font-size: 11px;
  color: #52c41a;
  font-weight: bold;
}

.sleep-card .effect {
  color: rgba(255, 255, 255, 0.9);
}

.daily-summary,
.character-status {
  margin-bottom: 24px;
}

.meal-dialog,
.bath-dialog,
.sleep-dialog {
  padding: 16px 0;
}

.meal-options,
.bath-options {
  margin: 16px 0;
}

.meal-radio-group,
.bath-radio-group {
  width: 100%;
}

.meal-option,
.bath-option {
  display: block;
  width: 100%;
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.meal-option:hover,
.bath-option:hover {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.meal-info,
.bath-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 8px;
}

.meal-name,
.bath-name {
  font-weight: bold;
}

.meal-effect,
.bath-effect {
  font-size: 12px;
  color: #666;
}

.meal-cost,
.bath-cost {
  font-size: 12px;
  color: #faad14;
  font-weight: bold;
}

.sleep-summary {
  margin: 16px 0;
}

.sleep-summary ul {
  margin: 8px 0;
  padding-left: 20px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}
</style>
