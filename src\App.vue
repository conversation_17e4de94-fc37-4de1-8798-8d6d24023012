<script setup lang="ts">
import { RouterView } from 'vue-router'
</script>

<template>
    <div id="app">
        <RouterView />
    </div>
</template>

<style>
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html,
body,
#app {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
