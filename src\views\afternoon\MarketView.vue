<template>
  <div class="market-view">
    <a-card title="翡翠商会" class="market-card">
      <template #extra>
        <a-space>
          <a-tag color="green">体力: {{ playerStatus.energy }}/{{ playerStatus.maxEnergy }}</a-tag>
          <a-tag color="gold">灵石: {{ playerStatus.money }}</a-tag>
          <a-button @click="goBack">返回地图</a-button>
        </a-space>
      </template>

      <a-tabs v-model:activeKey="activeTab">
        <!-- 购买材料 -->
        <a-tab-pane key="buy" tab="购买材料">
          <div class="market-section">
            <a-row gutter={16}>
              <a-col v-for="material in materials" :key="material.id" span={8}>
                <a-card hoverable class="item-card">
                  <template #cover>
                    <div class="item-icon material-icon">
                      <ExperimentOutlined />
                    </div>
                  </template>
                  <a-card-meta :title="material.name" :description="material.description" />
                  <div class="item-info">
                    <div class="price">价格: {{ material.price }} 灵石</div>
                    <div class="stock">库存: {{ material.stock }}</div>
                  </div>
                  <div class="item-actions">
                    <a-input-number v-model:value="material.buyQuantity" :min="1"
                      :max="Math.min(material.stock, Math.floor(playerStatus.money / material.price))"
                      style="width: 80px" />
                    <a-button type="primary" @click="buyMaterial(material)" :disabled="!canBuy(material)">
                      购买
                    </a-button>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 出售商品 -->
        <a-tab-pane key="sell" tab="出售商品">
          <div class="market-section">
            <a-row gutter={16}>
              <a-col v-for="product in sellableProducts" :key="product.id" span={8}>
                <a-card hoverable class="item-card">
                  <template #cover>
                    <div class="item-icon product-icon">
                      <GiftOutlined />
                    </div>
                  </template>
                  <a-card-meta :title="product.name" :description="product.description" />
                  <div class="item-info">
                    <div class="price">售价: {{ product.sellPrice }} 灵石</div>
                    <div class="owned">拥有: {{ product.quantity }}</div>
                  </div>
                  <div class="item-actions">
                    <a-input-number v-model:value="product.sellQuantity" :min="1" :max="product.quantity"
                      style="width: 80px" />
                    <a-button type="primary" @click="sellProduct(product)" :disabled="product.quantity === 0">
                      出售
                    </a-button>
                  </div>
                </a-card>
              </a-col>
            </a-row>
            <a-empty v-if="sellableProducts.length === 0" description="暂无可出售的商品" />
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ExperimentOutlined, GiftOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { gameStore } from '@/stores/gameStore'
import { getItemById } from '@/utils/gameData'

interface Material {
  id: string
  name: string
  description: string
  price: number
  stock: number
  buyQuantity: number
}

interface Product {
  id: string
  name: string
  description: string
  sellPrice: number
  quantity: number
  sellQuantity: number
}

const router = useRouter()
const activeTab = ref('buy')

// 从状态管理获取玩家状态
const { playerStatus, consumeEnergy, spendMoney, gainMoney, addItem, getItemQuantity } = gameStore

// 材料数据
const materials = ref<Material[]>([
  {
    id: 'seed',
    name: '灵果种子',
    description: '用于种植的神奇种子',
    price: 5,
    stock: 50,
    buyQuantity: 1
  },
  {
    id: 'spring_water',
    name: '清泉水',
    description: '制作圣水的基础材料',
    price: 10,
    stock: 30,
    buyQuantity: 1
  },
  {
    id: 'flower_nectar',
    name: '花蜜',
    description: '制作蜜酿的核心材料',
    price: 15,
    stock: 20,
    buyQuantity: 1
  }
])

// 可出售的商品（从库存中获取）
const sellableProducts = computed(() => {
  const sellableIds = ['holy_water', 'honey_wine', 'lotion', 'nectar']
  return sellableIds.map(itemId => {
    const quantity = getItemQuantity(itemId)
    if (quantity === 0) return null

    const item = getItemById(itemId)
    if (!item) return null

    return {
      id: itemId,
      name: item.name,
      description: item.description,
      sellPrice: item.value,
      quantity,
      sellQuantity: 1
    }
  }).filter(Boolean)
})

// 检查是否可以购买
const canBuy = (material: Material) => {
  return material.stock > 0 &&
    playerStatus.money >= material.price * material.buyQuantity &&
    playerStatus.energy > 0
}

// 购买材料
const buyMaterial = (material: Material) => {
  if (!canBuy(material)) {
    message.error('无法购买此材料')
    return
  }

  const totalCost = material.price * material.buyQuantity

  if (!spendMoney(totalCost)) {
    message.error('灵石不足')
    return
  }

  // 添加物品到库存
  addItem(material.id, material.buyQuantity)

  material.stock -= material.buyQuantity
  consumeEnergy(1)

  message.success(`购买了 ${material.buyQuantity} 个 ${material.name}，花费 ${totalCost} 灵石`)

  // 重置购买数量
  material.buyQuantity = 1

  // 体力耗尽自动返回
  if (playerStatus.energy <= 0) {
    message.warning('体力耗尽，自动返回地图')
    setTimeout(() => goBack(), 1000)
  }
}

// 出售商品
const sellProduct = (product: any) => {
  if (product.quantity === 0) {
    message.error('没有可出售的商品')
    return
  }

  const totalEarning = product.sellPrice * product.sellQuantity

  // 从库存中移除物品
  if (!gameStore.removeItem(product.id, product.sellQuantity)) {
    message.error('库存不足')
    return
  }

  gainMoney(totalEarning)
  consumeEnergy(1)

  message.success(`出售了 ${product.sellQuantity} 个 ${product.name}，获得 ${totalEarning} 灵石`)

  // 重置出售数量
  product.sellQuantity = 1

  // 体力耗尽自动返回
  if (playerStatus.energy <= 0) {
    message.warning('体力耗尽，自动返回地图')
    setTimeout(() => goBack(), 1000)
  }
}

const goBack = () => {
  router.push({ name: 'map' })
}
</script>

<style scoped>
.market-view {
  height: auto;
  padding: 16px;
}

.market-card {
  height: 100%;
}

.market-section {
  padding: 16px 0;
}

.item-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.item-icon {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
}

.material-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.product-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.item-info {
  margin: 12px 0;
}

.price,
.stock,
.owned {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.item-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}
</style>
